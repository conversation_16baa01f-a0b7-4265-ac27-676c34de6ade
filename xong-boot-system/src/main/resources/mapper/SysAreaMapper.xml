<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.SysAreaMapper">
    <resultMap type="com.xong.boot.system.domain.SysArea" id="SysAreaResult">
        <result property="areaId"    column="area_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="level"    column="level"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="areaCode"    column="area_code"    />
        <result property="adCode"    column="ad_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="address"    column="address"    />
        <result property="enabled"    column="enabled"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAreaVo">
        select area_id, parent_id, level, ancestors, area_code, area_name, ad_code, address, enabled, sort_order, remark from sys_area
    </sql>

    <select id="selectSysAreaList" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code like concat('%', #{areaCode}, '%')</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="enabled != null  and enabled != ''"> and enabled = #{enabled}</if>
        </where>
        order by sort_order asc
    </select>
</mapper>
