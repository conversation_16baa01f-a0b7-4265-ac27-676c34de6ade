<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.SysDeptMapper">
    <resultMap id="DeptResultMap" type="map" extends="com.xong.boot.common.mapper.BaseMapper.BaseResultMap">
        <id column="dept_id" property="deptId"/>
        <result column="parent_id" property="parentId"/>
        <result column="full_path" property="fullPath" typeHandler="com.xong.boot.common.handlers.ArrayStringTypeHandler"/>
        <result column="dept_code" property="deptCode"/>
        <result column="dept_name" property="deptName"/>
        <result column="dept_short_name" property="deptShortName"/>
        <result column="dept_type" property="deptType"/>
        <result column="district_ids" property="districtIds" typeHandler="com.xong.boot.common.handlers.ArrayStringTypeHandler"/>
        <result column="leader_id" property="leaderId"/>
        <result column="address" property="address"/>
        <result column="phone" property="phone"/>
        <result column="tel" property="tel"/>
        <result column="email" property="email"/>
        <result column="sort_on" property="sortOn"/>
        <result column="status" property="status"/>
        <association property="parent" columnPrefix="p_" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DeptItemResultMap"/>
        <association property="leader" columnPrefix="lu_" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.UserItemResultMap"/>
        <collection column="districtIds" property="districts" select="com.xong.boot.system.mapper.SysDistrictMapper.selectDistricts" />
    </resultMap>

    <!--  查询部门详情  -->
    <select id="selectDeptById" resultMap="DeptResultMap">
        SELECT t1.dept_id,
               t1.parent_id,
               t1.full_path,
               t1.dept_code,
               t1.dept_name,
               t1.dept_short_name,
               t1.dept_type,
               t1.district_ids,
               t1.district_ids districtIds,
               t1.leader_id,
               t1.address,
               t1.phone,
               t1.tel,
               t1.email,
               t1.sort_on,
               t1.`status`,
               t1.remark,
               t1.create_by,
               t1.create_time,
               t1.update_by,
               t1.update_time,
               t2.dept_id         p_dept_id,
               t2.parent_id       p_parent_id,
               t2.full_path       p_full_path,
               t2.dept_code       p_dept_code,
               t2.dept_name       p_dept_name,
               t2.dept_short_name p_dept_short_name,
               t2.dept_type       p_dept_type,
               t2.district_ids    p_district_ids,
               t2.leader_id       p_leader_id,
               t2.address         p_address,
               t2.phone           p_phone,
               t2.tel             p_tel,
               t2.email           p_email,
               t2.`status`        p_status,
               t3.user_id         lu_user_id,
               t3.user_code       lu_user_code,
               t3.username        lu_username,
               t3.avatar          lu_avatar,
               t3.realname        lu_realname,
               t3.pinyin          lu_pinyin,
               t3.phone           lu_phone,
               t3.id_card         lu_id_card,
               t3.email           lu_email,
               t3.dept_id         lu_dept_id,
               t3.district_ids    lu_district_ids,
               t3.birthday        lu_birthday,
               t3.gender          lu_gender,
               t3.`status`        lu_status
        FROM (
            SELECT *
            FROM sys_dept
            WHERE !del_flag AND dept_id = #{deptId}
        ) t1
        LEFT JOIN (
            SELECT *
            FROM sys_dept
            WHERE !del_flag
        ) t2 ON t1.parent_id = t2.dept_id
        LEFT JOIN (
            SELECT *
            FROM sys_user
            WHERE !del_flag
        ) t3 ON t1.leader_id = t3.user_id
    </select>

    <select id="selectListDept" resultType="map">
        SELECT t.dept_id, t.parent_id, t.full_path, t.dept_code, t.dept_name,
            t.dept_short_name, t.dept_type, t.status, t.leaf_count
        FROM (
            SELECT a1.*, COUNT(DISTINCT a2.dept_id) leaf_count
            FROM (
                SELECT *
                FROM sys_dept
                WHERE !del_flag
            ) a1 LEFT JOIN (
                SELECT dept_id, parent_id
                FROM sys_dept
                WHERE !del_flag
            ) a2 ON a1.dept_id = a2.parent_id
            GROUP BY a1.dept_id
            ORDER BY a1.sort_on ASC, a1.create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <select id="selectPageRecycle" resultType="SysDept">
        SELECT t.dept_id,
               t.parent_id,
               t.full_path,
               t.dept_code,
               t.dept_name,
               t.dept_short_name,
               t.dept_type,
               t.phone,
               t.tel,
               t.email,
               t.status,
               t.remark,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time
        FROM (
            SELECT *
            FROM sys_dept
            WHERE del_flag
            ORDER BY sort_on ASC, create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <update id="restoreDelFlag">
        UPDATE sys_dept
        SET del_flag=0
        WHERE dept_id IN
        <foreach collection="deptIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectNotParentDeptId" resultType="string">
        SELECT t1.dept_id
        FROM (
            SELECT dept_id, parent_id
            FROM sys_dept
            WHERE parent_id IS NOT NULL AND parent_id != '' AND dept_id IN <foreach collection="deptIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        ) t1 LEFT JOIN (
            SELECT dept_id
            FROM sys_dept
            WHERE del_flag
        ) t2 ON t1.parent_id = t2.dept_id
        WHERE t2.dept_id IS NOT NULL
    </select>
</mapper>
