<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.SysMenuMapper">
  <select id="selectListMenu" resultType="map">
      SELECT t.*
      FROM (
        SELECT t1.menu_id,
            t1.parent_id,
            t1.menu_type,
            t1.menu_code,
            t1.path,
            t1.name,
            t1.component,
            t1.redirect,
            t1.hidden,
            t1.hide_children_in_menu,
            t1.title,
            t1.icon,
            t1.target,
            t1.keep_alive,
            t1.hidden_breadcrumb,
            t1.affix,
            t1.sort_on,
            t1.status,
            t1.remark,
            t1.create_by,
            t1.create_time,
            t1.update_by,
            t1.update_time,
            IFNULL(t2.rule_count, 0) rule_count
        FROM sys_menu t1
        LEFT JOIN (
            SELECT menu_id, COUNT(menu_id) rule_count
            FROM sys_rule
            GROUP BY menu_id
        ) t2 ON t1.menu_id = t2.menu_id
        ORDER BY t1.sort_on ASC, t1.create_time ASC , t1.menu_id ASC
    ) t ${ew.customSqlSegment}
  </select>
</mapper>
