<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.UserDetailsMapper">
    <sql id="SelectUserSql">
        SELECT user_id, user_code, username, password, salt, usb_key, avatar, realname,
               pinyin, phone, id_card, email, dept_id, district_ids, birthday, gender,
               account_expired, credentials_expired, sort_on, status, del_flag,
               remark, create_by, create_time, update_by, update_time
        FROM sys_user
    </sql>

    <!--  查询用户  -->
    <select id="selectByUserId" resultMap="com.xong.boot.common.mapper.BaseMapper.UserResultMap">
        <include refid="SelectUserSql" />
        WHERE user_id = #{userId}
    </select>

    <!--  查询用户  -->
    <select id="selectByUsername" resultMap="com.xong.boot.common.mapper.BaseMapper.UserResultMap">
        <include refid="SelectUserSql" />
        WHERE username = #{username}
    </select>

    <!--  查询当前部门  -->
    <select id="selectDeptById" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DeptItemResultMap">
        SELECT dept_id, parent_id, full_path, dept_code, dept_name, dept_short_name, dept_type, district_ids, leader_id,
            address, phone, tel, email, `status`
        FROM sys_dept
        WHERE `status` = 0 AND dept_id = #{deptId}
    </select>

    <!--  查询用户部门  -->
    <select id="selectDeptByUserId" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DeptItemResultMap">
        SELECT t2.dept_id, t2.parent_id, t2.full_path, t2.dept_code, t2.dept_name, t2.dept_short_name, t2.dept_type, t2.district_ids,
            t2.leader_id, t2.address, t2.phone, t2.tel, t2.email, t2.status, t2.sort_on
        FROM (
            SELECT dept_id
            FROM sys_user_dept
            WHERE user_id = #{userId}
        ) t1 INNER JOIN sys_dept t2 ON t1.dept_id = t2.dept_id
        WHERE t2.status = 0
        ORDER BY t2.sort_on ASC
    </select>

    <!--  查询地区  -->
    <select id="selectDistrictById" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DistrictItemResultMap">
        SELECT id, parent_id, city_code, name, short_name, level, center, status
        FROM sys_district
        WHERE `status` = 0 AND id = #{id}
    </select>

    <!--  查询用户职务  -->
    <select id="selectPositionByUserId" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.PositionItemResultMap">
        SELECT t2.post_id, t2.post_code, t2.post_name, t2.post_level, t2.status, t2.sort_on
        FROM (
            SELECT post_id
            FROM sys_user_position
            WHERE user_id = #{userId}
        ) t1 INNER JOIN sys_position t2 ON t1.post_id = t2.post_id
        WHERE t2.status = 0
        ORDER BY t2.sort_on ASC
    </select>

    <!--  查询用户角色  -->
    <select id="selectRole" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.RoleItemResultMap">
        SELECT DISTINCT t2.role_id, t2.role_code, t2.role_name, t2.status,t2.es_query_perm
        FROM (
            SELECT role_id
            FROM sys_user_role
            WHERE user_id = #{userId} AND (dept_id IS NULL OR dept_id = ''<if test="deptId != null and deptId != ''"> OR dept_id = #{deptId}</if>)
        ) t1 Inner Join sys_role t2 ON t1.role_id = t2.role_id
        WHERE t2.status = 0
    </select>

    <!--  查询用户权限  -->
    <select id="selectPermission" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.PermissionItemResultMap">
        SELECT t3.menu_id permission_id, t3.menu_code permission_code, t3.title permission_name, t3.status, t3.sort_on
        FROM (
            SELECT user_id, role_id
            FROM sys_user_role
            WHERE user_id = #{userId} AND (dept_id IS NULL OR dept_id = ''<if test="deptId != null and deptId != ''"> OR dept_id = #{deptId}</if>)
        ) t1 INNER JOIN sys_role_menu t2 ON t1.role_id = t2.role_id
        INNER JOIN (
            SELECT menu_id, menu_code, title, sort_on, status
            FROM sys_menu
            WHERE status = 0 AND menu_code IS NOT NULL AND menu_code != ''
        ) t3 ON t2.menu_id = t3.menu_id
        ORDER BY t3.sort_on ASC
    </select>

    <!--  查询用户菜单  -->
    <select id="selectMenu" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.MenuItemResultMap">
        SELECT DISTINCT t3.menu_id, t3.parent_id, t3.path, t3.name, t3.component, t3.redirect,
            t3.hidden,t3.hide_children_in_menu, t3.title, t3.icon, t3.target, t3.keep_alive,
            t3.hidden_breadcrumb, t3.affix, t3.status, t3.sort_on, t3.create_time
        FROM (
            SELECT role_id
            FROM sys_user_role
            WHERE user_id = #{userId} AND (dept_id IS NULL OR dept_id = ''<if test="deptId != null and deptId != ''"> OR dept_id = #{deptId}</if>)
        ) t1 INNER JOIN sys_role_menu t2 ON t1.role_id = t2.role_id
        INNER JOIN (
            SELECT menu_id, parent_id, path, `name`, component, redirect, hidden, hide_children_in_menu,
                title, icon, target, keep_alive, hidden_breadcrumb, affix, sort_on, `status`, create_time
            FROM sys_menu
            WHERE status = 0 AND menu_type = 'MENU'
        ) t3 ON t2.menu_id = t3.menu_id
        ORDER BY t3.sort_on ASC, t3.create_time ASC, t3.menu_id ASC
    </select>

    <select id="selectRule" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.RuleItemResultMap">
        SELECT rule_id, menu_id, rule_name, rule_condition, rule_column, rule_value, `status`
        FROM sys_rule
        WHERE `status` = 0 AND rule_id IN
        <foreach collection="ruleIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--  查询用户相关接口数据规则  -->
    <select id="selectPathRuleIds" resultType="com.xong.boot.common.model.PathRuleIds">
        SELECT DISTINCT t3.path, t2.rule_ids
        FROM (
            SELECT user_id, role_id
            FROM sys_user_role
            WHERE user_id = #{userId} AND (dept_id IS NULL OR dept_id = ''<if test="deptId != null and deptId != ''"> OR dept_id = #{deptId}</if>)
        ) t1 INNER JOIN (
            SELECT role_id, menu_id, rule_ids
            FROM sys_role_menu
            WHERE rule_ids IS NOT NULL AND rule_ids != ''
        ) t2 ON t1.role_id = t2.role_id
        INNER JOIN (
            SELECT menu_id, path
            FROM sys_menu
            WHERE `status` = 0 AND menu_type = 'PATH' AND path IS NOT NULL AND path != ''
        ) t3 ON t2.menu_id = t3.menu_id
    </select>
</mapper>
