<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.SysDistrictMapper">
    <!-- 查询用户地区 -->
    <select id="selectDistricts" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DistrictItemResultMap">
        SELECT id, parent_id, city_code, `name`, short_name, `level`, center, status
        FROM sys_district
        WHERE
        <choose>
            <when test="districtIds != null and districtIds != ''">
                id IN
                <foreach collection="districtIds.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                ORDER BY id ASC
            </when>
            <otherwise>
                1 != 1
            </otherwise>
        </choose>
    </select>
</mapper>
