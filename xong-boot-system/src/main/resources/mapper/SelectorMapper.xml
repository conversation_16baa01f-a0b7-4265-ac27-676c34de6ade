<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xong.boot.system.mapper.SelectorMapper">
    <select id="listDept" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DeptItemResultMap">
        SELECT dept_id,
               parent_id,
               full_path,
               dept_code,
               dept_name,
               dept_short_name,
               dept_type,
               district_ids,
               leader_id,
               address,
               phone,
               tel,
               email,
               `status`
        FROM (
            SELECT *
            FROM sys_dept
            WHERE !del_flag
            ORDER BY sort_on ASC, create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <select id="pageUser" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.UserItemResultMap">
        SELECT t.user_id, t.user_code, t.username, t.avatar, t.realname, t.pinyin, t.phone,
               t.id_card, t.email, t.dept_id, t.district_ids, t.birthday, t.gender, t.`status`
        FROM (
            SELECT a1.user_id, a1.user_code, a1.username, a1.avatar, a1.realname, a1.pinyin,
                a1.phone, a1.id_card, a1.email, a1.dept_id, a1.district_ids, a1.birthday,
                a1.gender, a1.sort_on, a1.`status`, a3.full_path, a3.dept_code, a3.dept_name,
                a1.create_time
            FROM (
                SELECT user_id, user_code, username, avatar, realname, pinyin, phone, id_card,
                    email, dept_id, district_ids, birthday, gender, sort_on, `status`, create_time
                FROM sys_user
                WHERE !del_flag
            ) a1 LEFT JOIN sys_user_dept a2 ON a1.user_id = a2.user_id
            LEFT JOIN (
                SELECT dept_id, full_path, dept_code, dept_name
                FROM sys_dept
                WHERE !del_flag
            ) a3 ON a1.dept_id = a3.dept_id
        <choose>
            <when test="userScope == 1">
                GROUP BY a1.user_id
                HAVING COUNT(a3.dept_id) > 0
            </when>
            <when test="userScope == 2">
                GROUP BY a1.user_id
                HAVING COUNT(a3.dept_id) = 0
            </when>
            <when test="userScope == 3">
                WHERE a3.dept_id = #{deptId}
            </when>
            <when test="userScope == 4">
                WHERE a3.full_path IS NOT NULL AND CONCAT(',', a3.full_path, ',') LIKE CONCAT('%,', #{deptId}, ',%')
                GROUP BY a1.user_id
            </when>
            <when test="userScope == 5">
                WHERE a3.full_path IS NOT NULL AND CONCAT(',', a3.full_path) LIKE CONCAT('%,', #{deptId}, ',%')
                GROUP BY a1.user_id
            </when>
            <otherwise>
                GROUP BY a1.user_id
            </otherwise>
        </choose>
            ORDER BY a1.sort_on ASC, a1.create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <select id="listRole" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.RoleItemResultMap">
        SELECT role_id, role_code, role_name, `status`
        FROM (
            SELECT role_id, role_code, role_name, `status`
            FROM sys_role
            ORDER BY create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <select id="listPosition" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.PositionItemResultMap">
        SELECT post_id, post_code, post_name, post_level, `status`
        FROM (
            SELECT post_id, post_code, post_name, post_level, `status`, sort_on, create_time
            FROM sys_position
            ORDER BY sort_on ASC, post_level ASC, create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

</mapper>
