<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.SysRoleMapper">
    <select id="selectPageRoleUser" resultType="com.xong.boot.system.model.resp.RoleUserResp">
        SELECT t.user_id, t.user_code, t.username, t.realname, t.pinyin, t.phone,
            t.id_card, t.email, t.birthday, t.gender, t.`status`, t.user_role_dept_id,
            t.user_role_dept_name, t.role_id, t.user_role_id
        FROM (
            SELECT a1.user_id, a1.user_code, a1.username, a1.realname, a1.pinyin, a1.phone,
                a1.id_card, a1.email, a1.birthday, a1.gender, a1.`status`, a2.dept_id user_role_dept_id,
                a3.dept_name user_role_dept_name, a2.role_id, a2.id user_role_id
            FROM sys_user a1
            LEFT JOIN sys_user_role a2 ON a1.user_id = a2.user_id
            LEFT JOIN sys_dept a3 ON a2.dept_id = a3.dept_id
            WHERE !a1.del_flag <choose>
                <when test="type != null and type == 1">
                    AND a2.role_id = #{roleId}
                    GROUP BY a1.user_id
                </when>
                <otherwise>
                    GROUP BY a1.user_id
                    HAVING SUM(IFNULL(a2.role_id, '') = #{roleId}) = 0
                </otherwise>
            </choose>
            ORDER BY a1.sort_on ASC, a1.create_time DESC
        ) t ${ew.customSqlSegment}
    </select>
</mapper>
