<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xong.boot.system.mapper.SysUserMapper">
    <resultMap id="SysUserResultMap" extends="com.xong.boot.common.mapper.BaseMapper.UserResultMap" type="SysUser">
    </resultMap>

    <select id="selectByUserId" resultMap="SysUserResultMap">
        SELECT t1.*,
               t2.user_id         lu_user_id,
               t2.user_code       lu_user_code,
               t2.avatar          lu_avatar,
               t2.username        lu_username,
               t2.realname        lu_realname,
               t2.pinyin          lu_pinyin,
               t2.phone           lu_phone,
               t2.id_card         lu_id_card,
               t2.email           lu_email,
               t2.birthday        lu_birthday,
               t2.gender          lu_gender,
               t2.`status`        lu_status,
               t3.dept_id         d_dept_id,
               t3.parent_id       d_parent_id,
               t3.full_path       d_full_path,
               t3.dept_code       d_dept_code,
               t3.dept_name       d_dept_name,
               t3.dept_short_name d_dept_short_name,
               t3.dept_type       d_dept_type,
               t3.`status`        d_status,
               t4.dept_id         cd_dept_id,
               t4.parent_id       cd_parent_id,
               t4.full_path       cd_full_path,
               t4.dept_code       cd_dept_code,
               t4.dept_name       cd_dept_name,
               t4.dept_short_name cd_dept_short_name,
               t4.dept_type       cd_dept_type,
               t4.`status`        cd_status,
               t4.role_ids        cd_role_ids
        FROM sys_user t1
                 LEFT JOIN (
            SELECT user_id,
                   user_code,
                   avatar,
                   username,
                   realname,
                   pinyin,
                   phone,
                   id_card,
                   email,
                   birthday,
                   gender,
                   `status`
            FROM sys_user
            WHERE !del_flag
        ) t2 ON t2.user_id = t1.leader_id
                 LEFT JOIN (
            SELECT sd.dept_id,
                   sd.parent_id,
                   sd.full_path,
                   sd.dept_code,
                   sd.dept_name,
                   sd.dept_short_name,
                   sd.dept_type,
                   sd.`status`,
                   sud.user_id
            FROM sys_dept sd
            INNER JOIN sys_user_dept sud ON sud.dept_id = sd.dept_id
            WHERE !sd.del_flag
        ) t3 ON t3.user_id = t1.user_id
        LEFT JOIN (
            SELECT sd.dept_id,
                   sd.parent_id,
                   sd.full_path,
                   sd.dept_code,
                   sd.dept_name,
                   sd.dept_short_name,
                   sd.dept_type,
                   sd.`status`,
                   sud.user_id,
                   sud.role_ids
            FROM sys_dept sd
            INNER JOIN sys_user_dept sud ON sud.dept_id = sd.dept_id
            WHERE !sd.del_flag AND sud.active
        ) t4 ON t4.user_id = t1.user_id
        WHERE t1.user_id = #{userId}
    </select>

    <select id="selectPageUser" resultMap="com.xong.boot.common.mapper.BaseMapper.UserResultMap">
        SELECT *
        FROM (
            SELECT t1.*
            FROM (SELECT * FROM sys_user WHERE !del_flag) t1
            LEFT JOIN sys_user_dept t2 ON t1.user_id = t2.user_id
            LEFT JOIN (SELECT * FROM sys_dept WHERE !del_flag) t3 ON t2.dept_id = t3.dept_id
            <choose>
            <when test="type == 1">
                GROUP BY t1.user_id
                HAVING COUNT(t3.dept_id) > 0
            </when>
            <when test="type == 2">
                GROUP BY t1.user_id
                HAVING COUNT(t3.dept_id) = 0
            </when>
            <when test="type == 3">
                WHERE t3.dept_id = #{deptId}
            </when>
            <when test="type == 4">
                WHERE t3.full_path IS NOT NULL AND CONCAT(',', t3.full_path, ',') LIKE CONCAT('%,', #{deptId}, ',%')
                GROUP BY t1.user_id
            </when>
            <when test="type == 5">
                WHERE t3.full_path IS NOT NULL AND CONCAT(',', t3.full_path) LIKE CONCAT('%,', #{deptId}, ',%')
                GROUP BY t1.user_id
            </when>
            <otherwise>
                GROUP BY t1.user_id
            </otherwise>
            </choose>
            ORDER BY t1.sort_on ASC, t1.create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <select id="selectPageRecycle" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.UserItemResultMap">
        SELECT t.user_id, t.user_code, t.username, t.avatar, t.realname, t.pinyin, t.phone,
        t.id_card, t.email, t.birthday, t.gender, t.`status`
        FROM (
            SELECT user_id, user_code, username, avatar, realname, pinyin, phone,
                id_card, email, birthday, gender, `status`
            FROM sys_user
            WHERE del_flag
            ORDER BY sort_on ASC, create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <update id="restoreDelFlag">
        UPDATE sys_user
        SET del_flag=0
        WHERE user_id IN
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectPageUserRole" resultType="com.xong.boot.system.model.resp.UserRoleResp">
        SELECT t.role_id, t.role_code, t.role_name, t.`status`, t.dept_id, t.dept_name,
            t.user_id, t.user_role_id
        FROM (
            SELECT a1.role_id, a1.role_code, a1.role_name, a1.`status`, a3.dept_id,
                a3.dept_name, a2.user_id, a2.id user_role_id
            FROM sys_role a1
            LEFT JOIN sys_user_role a2 ON a1.role_id = a2.role_id
            LEFT JOIN sys_dept a3 ON a2.dept_id = a3.dept_id
            <choose>
                <when test="type != null and type == 1">
                    WHERE a2.user_id = #{userId}
                    GROUP BY a1.role_id
                </when>
                <otherwise>
                    GROUP BY a1.role_id
                    HAVING SUM(IFNULL(a2.user_id, '') = #{userId}) = 0
                </otherwise>
            </choose>
            ORDER BY a1.create_time DESC
        ) t ${ew.customSqlSegment}
    </select>

    <select id="selectListUserDept" resultMap="com.xong.boot.framework.mapper.ApplicationMapper.DeptItemResultMap">
        SELECT t1.dept_id, t1.parent_id, t1.full_path, t1.dept_code, t1.dept_name, t1.dept_short_name,
               t1.dept_type, t1.district_ids, t1.leader_id, t1.address, t1.phone, t1.tel, t1.email, t1.status
        FROM sys_dept t1
        INNER JOIN sys_user_dept t2 ON t1.dept_id = t2.dept_id
        WHERE t2.user_id = #{userId}
        ORDER BY t1.sort_on ASC, t1.create_time DESC
    </select>

    <select id="selectExistUsername" resultType="boolean">
        SELECT count(1)
        FROM sys_user
        WHERE username = #{username}
    </select>

    <select id="selectExistUserCode" resultType="boolean">
        SELECT count(1)
        FROM sys_user
        WHERE user_code = #{userCode}
        <if test="userId != null and userId != ''">
            AND user_id != #{userId}
        </if>
    </select>

    <select id="selectExistPhone" resultType="boolean">
        SELECT count(1)
        FROM sys_user
        WHERE phone = #{phone}
        <if test="userId != null and userId != ''">
            AND user_id != #{userId}
        </if>
    </select>

    <select id="selectExistIdCard" resultType="boolean">
        SELECT count(1)
        FROM sys_user
        WHERE id_card = #{idCard}
        <if test="userId != null and userId != ''">
            AND user_id != #{userId}
        </if>
    </select>

    <select id="selectExistEmail" resultType="boolean">
        SELECT count(1)
        FROM sys_user
        WHERE email = #{email}
        <if test="userId != null and userId != ''">
            AND user_id != #{userId}
        </if>
    </select>
</mapper>
