package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.domain.SysUserRole;
import com.xong.boot.system.mapper.SysRoleMapper;
import com.xong.boot.system.mapper.SysUserRoleMapper;
import com.xong.boot.system.model.resp.RoleUserResp;
import com.xong.boot.system.service.SysRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色管理 service
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl extends BaseServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    private final SysUserRoleMapper userRoleMapper;

    public SysRoleServiceImpl(SysUserRoleMapper userRoleMapper) {
        this.userRoleMapper = userRoleMapper;
    }

    /**
     * 分页根据角色ID获取用户列表
     * @param page         分页
     * @param roleId       角色ID
     * @param type         1包含角色ID的用户 2不包含角色ID的用户
     * @param queryWrapper 条件构建器
     */
    @Override
    public Page<RoleUserResp> pageRoleUser(Page<RoleUserResp> page, String roleId, Integer type, Wrapper<SysUser> queryWrapper) {
        return baseMapper.selectPageRoleUser(page, roleId, type, queryWrapper);
    }

    /**
     * 存在角色编码
     * @param roleCode 角色编码
     * @param roleId   排除角色
     */
    @Override
    public void existRoleCode(String roleCode, String roleId) {
        LambdaQueryWrapper<SysRole> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysRole::getRoleCode, roleCode);
        if (StringUtils.isNotBlank(roleId)) {
            queryWrapper.ne(SysRole::getRoleId, roleId);
        }
        long count = count(queryWrapper);
        if (count > 0) {
            throw new XServerException("角色编码已存在");
        }
    }

    /**
     * 存在关联用户
     * @param roleIds 角色ID
     */
    @Override
    public void existRoleUser(List<String> roleIds) {
        LambdaQueryWrapper<SysUserRole> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(SysUserRole::getRoleId, roleIds);
        long count = userRoleMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new XServerException("角色绑定了用户不允许删除");
        }
    }
}
