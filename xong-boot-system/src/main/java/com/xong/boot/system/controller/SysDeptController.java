package com.xong.boot.system.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysDept;
import com.xong.boot.system.service.SysDeptService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 部门管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/dept")
public class SysDeptController extends BaseController<SysDeptService, SysDept> {
    /**
     * 新增部门
     * @param dept 部门信息
     */
    @PostMapping
    @XLog(title = "新增部门", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:dept:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysDept dept) {
        if (baseService.saveDept(dept)) {
            return Result.success("新增部门成功");
        }
        return Result.fail("新增部门失败");
    }

    /**
     * 删除部门
     * @param ids 部门ID
     */
    @DeleteMapping
    @XLog(title = "删除部门", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:dept:delete')")
    public Result delete(@NotEmpty(message = "部门ID不存在") String[] ids) {
        List<String> idList = Arrays.asList(ids);
        long count = baseService.count(Wrappers.lambdaQuery(SysDept.class).in(SysDept::getParentId, idList));
        if (count > 0) {
            return Result.fail("存在下级部门不允许删除");
        }
        if (baseService.removeByIds(idList)) {
            return Result.success("部门删除成功");
        }
        return Result.fail("部门删除失败");
    }

    /**
     * 编辑部门
     * @param dept 部门参数
     */
    @PutMapping
    @XLog(title = "编辑部门", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:dept:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysDept dept) {
        baseService.updateDept(dept);
        return Result.success("部门修改成功");
    }

    /**
     * 获取部门详情
     * @param id 部门ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:dept:*')")
    public Result detail(@NotBlank(message = "部门ID不存在") String id) {
        return Result.successData(baseService.getDeptById(id));
    }

    /**
     * 获取部门信息
     * @param parentId 父级部门
     * @param search   搜索
     */
    @GetMapping("/list")
    @PreAuthorize("hasAnyAuthority('system:dept:list')")
    public Result list(String parentId, String search) {
        XQueryWrapper<SysDept> queryWrapper = XQueryWrapper.<SysDept>newInstance().startDataScope();
        if (parentId != null) {
            queryWrapper.eq("parent_id", parentId);
        }
        if (StringUtils.isNotBlank(search)) {
            queryWrapper.and(qw -> qw.like("dept_code", search)
                    .or().like("dept_name", search));
        }
        return Result.successData(baseService.listDept(queryWrapper));
    }

    /**
     * 分页获取已删除部门
     * @param dept 查询条件
     */
    @GetMapping("/page/recycle")
    @PreAuthorize("hasAuthority('system:dept:recycle')")
    public Result pageRecycle(SysDept dept) {
        QueryWrapper<SysDept> queryWrapper = XQueryWrapper.newInstance(dept)
                .entity2Condition()
                .startAdvancedQuery()
                .startSort();
        return Result.successData(baseService.pageRecycle(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 还原已删除部门
     * @param params 查询条件
     */
    @PutMapping("/restore")
    @XLog(title = "还原已删除部门", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:dept:recycle')")
    public Result restore(@RequestBody @NotNull(message = "参数错误") JSONObject params) {
        JSONArray deptIds = params.getJSONArray("deptIds");
        if (deptIds == null || deptIds.size() == 0) {
            return Result.fail("部门ID不存在");
        }
        if (baseService.restore(deptIds.toJavaList(String.class))) {
            return Result.success("部门还原成功");
        }
        return Result.success("部门还原失败");
    }

    /**
     * 部门拖拽修改 排序与全路径
     * @param params 拖拽后参数
     */
    @PutMapping("/drop")
    @PreAuthorize("hasAuthority('system:dept:edit')")
    public Result drop(@RequestBody DropParams params) {
        baseService.dropDept(params);
        return Result.success("修改成功");
    }
}
