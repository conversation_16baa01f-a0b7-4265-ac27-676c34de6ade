package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysDept;
import com.xong.boot.system.domain.SysPosition;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.service.SelectorService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 选择器
 * <AUTHOR>
 */
@RestController
@RequestMapping(Constants.API_ADMIN_ROOT_PATH + "/selector")
public class SelectorController {
    private final SelectorService selectorService;

    public SelectorController(SelectorService selectorService) {
        this.selectorService = selectorService;
    }

    /**
     * 分页获取用户部门
     * @param userScope 用户范围 0所有部门用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId    部门ID
     * @param search    搜索（用户姓名、拼音、用户编码）
     */
    @GetMapping("/user")
    public Result user(Integer userScope, String deptId, String search) {
        LambdaQueryWrapper<SysUser> queryWrapper = XQueryWrapper.<SysUser>newInstance()
                .startAdvancedQuery()
                .lambda();
        if (StringUtils.isNotBlank(search)) {
            queryWrapper.and(qw -> qw.like(SysUser::getPinyin, search)
                    .or().like(SysUser::getUsername, search)
                    .or().like(SysUser::getUserCode, search)
                    .or().like(SysUser::getRealname, search)
            );
        }
        queryWrapper.orderByAsc(SysUser::getSortOn)
                .orderByDesc(SysUser::getCreateTime);
        return Result.successData(selectorService.pageUser(QueryHelper.getPage(), userScope, deptId, queryWrapper));
    }

    /**
     * 部门选择组件
     * @param search 搜索 querys 自定义查询条件
     */
    @GetMapping("/dept")
    public Result dept(String search) {
        LambdaQueryWrapper<SysDept> queryWrapper = XQueryWrapper.<SysDept>newInstance()
                .startAdvancedQuery()
                .lambda();
        if (StringUtils.isNotBlank(search)) {
            queryWrapper.and(qw -> qw.like(SysDept::getDeptName, search)
                    .or().like(SysDept::getDeptCode, search)
            );
        }
        queryWrapper.orderByAsc(SysDept::getSortOn)
                .orderByDesc(SysDept::getCreateTime);
        return Result.successData(selectorService.listDept(queryWrapper));
    }

    /**
     * 角色选择器
     * @param search 搜索
     */
    @GetMapping("/role")
    public Result role(String search) {
        LambdaQueryWrapper<SysRole> queryWrapper = XQueryWrapper.<SysRole>newInstance()
                .startAdvancedQuery()
                .lambda();
        if (StringUtils.isNotBlank(search)) {
            queryWrapper.and(qw -> qw.like(SysRole::getRoleName, search)
                    .or().like(SysRole::getRoleCode, search));
        }
        queryWrapper.orderByDesc(SysRole::getCreateTime);
        return Result.successData(selectorService.listRole(queryWrapper));
    }

    /**
     * 职务选择器
     * @param search 搜索
     *               queryCondition 自定义查询条件
     */
    @GetMapping("/position")
    public Result position(String search) {
        LambdaQueryWrapper<SysPosition> queryWrapper = XQueryWrapper.<SysPosition>newInstance()
                .startAdvancedQuery()
                .lambda();
        if (StringUtils.isNotBlank(search)) {
            queryWrapper.and(qw -> qw.like(SysPosition::getPostName, search)
                    .or().like(SysPosition::getPostCode, search));
        }
        queryWrapper.orderByAsc(SysPosition::getSortOn, SysPosition::getPostLevel)
                .orderByDesc(SysPosition::getCreateTime);
        return Result.successData(selectorService.listPosition(queryWrapper));
    }
}
