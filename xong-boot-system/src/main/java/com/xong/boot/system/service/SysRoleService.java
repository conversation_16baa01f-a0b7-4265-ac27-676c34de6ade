package com.xong.boot.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.common.service.BaseService;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.framework.domain.items.UserRoleItem;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.model.resp.RoleUserResp;

import java.util.List;

/**
 * 角色管理
 * <AUTHOR>
 */
public interface SysRoleService extends BaseService<SysRole> {
    /**
     * 分页根据角色ID获取用户列表
     * @param page         分页
     * @param roleId       角色ID
     * @param type         1包含角色ID的用户 2不包含角色ID的用户
     * @param queryWrapper 条件构建器
     */
    Page<RoleUserResp> pageRoleUser(Page<RoleUserResp> page, String roleId, Integer type, Wrapper<SysUser> queryWrapper);

    /**
     * 存在角色编码
     * @param roleCode 角色编码
     * @param roleId   排除角色
     */
    void existRoleCode(String roleCode, String roleId);

    /**
     * 存在关联用户
     * @param roleIds 角色ID
     */
    void existRoleUser(List<String> roleIds);
}
