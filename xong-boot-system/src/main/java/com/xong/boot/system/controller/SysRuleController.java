package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.enums.QueryCondition;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysRule;
import com.xong.boot.system.service.SysRuleService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 数据规则管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/rule")
public class SysRuleController extends BaseController<SysRuleService, SysRule> {
    /**
     * 新增数据规则
     * @param rule 规则对象
     */
    @PostMapping
    @XLog(title = "新增数据规则", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:rule:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysRule rule) {
        QueryCondition ruleCondition = rule.getRuleCondition();
        if (ruleCondition == QueryCondition.IS_NULL || ruleCondition == QueryCondition.IS_NOT_NULL) {
            rule.setRuleValue(null);
        } else if (ruleCondition == QueryCondition.SQL) {
            rule.setRuleColumn(null);
        }
        if (baseService.save(rule)) {
            return Result.success("新增数据规则成功");
        }
        return Result.fail("新增数据规则失败");
    }

    /**
     * 删除数据规则
     * @param ids 规则ID集合
     */
    @DeleteMapping
    @XLog(title = "删除数据规则", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:rule:delete')")
    public Result delete(@NotEmpty(message = "规则ID不存在") String[] ids) {
        for (String id : ids) {
            if (baseService.existRoleMenu(id)) {
                return Result.fail("数据规则正在使用中");
            }
        }
        if (baseService.removeByIds(Arrays.asList(ids))) {
            return Result.success("数据规则删除成功");
        }
        return Result.fail("数据规则删除失败");
    }

    /**
     * 编辑数据规则
     * @param rule 规则对象
     */
    @PutMapping
    @XLog(title = "修改数据规则", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:rule:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysRule rule) {
        QueryCondition ruleCondition = rule.getRuleCondition();
        if (ruleCondition == QueryCondition.IS_NULL || ruleCondition == QueryCondition.IS_NOT_NULL) {
            rule.setRuleValue(null);
        } else if (ruleCondition == QueryCondition.SQL) {
            rule.setRuleColumn(null);
        }
        if (baseService.updateById(rule)) {
            return Result.success("数据规则修改成功");
        }
        return Result.fail("数据规则修改失败");
    }

    /**
     * 数据规则详情
     * @param id 规则ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:rule:*')")
    public Result detail(@NotNull(message = "规则ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 分页获取数据规则列表
     * @param params 查询条件
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:rule:list')")
    public Result page(SysRule params) {
        QueryWrapper<SysRule> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .orderByDesc("create_time");
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}
