package com.xong.boot.system.service.impl;

import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.system.domain.SysUserDept;
import com.xong.boot.system.mapper.SysUserDeptMapper;
import com.xong.boot.system.service.SysUserDeptService;
import org.springframework.stereotype.Service;

/**
 * 用户部门关系 service
 * <AUTHOR>
 */
@Service
public class SysUserDeptServiceImpl extends BaseServiceImpl<SysUserDeptMapper, SysUserDept> implements SysUserDeptService {
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateRole(SysUserDept params) {
//        if (StringUtils.isBlank(params.getUserId())) {
//            throw new XServerException("用户ID不存在");
//        }
//        if (StringUtils.isBlank(params.getDeptId())) {
//            throw new XServerException("部门ID不存在");
//        }
//        LambdaUpdateWrapper<SysUserDept> updateWrapper = Wrappers.lambdaUpdate(SysUserDept.class)
//                .eq(SysUserDept::getUserId, params.getUserId())
//                .eq(SysUserDept::getDeptId, params.getDeptId())
//                .set(SysUserDept::getRoleIds, String.join(",", params.getRoleIds()));
//        update(updateWrapper);
//    }
}
