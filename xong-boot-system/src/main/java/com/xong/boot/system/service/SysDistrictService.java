package com.xong.boot.system.service;

import com.xong.boot.common.service.BaseService;
import com.xong.boot.framework.domain.items.DistrictItem;
import com.xong.boot.system.domain.SysDistrict;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 地区 Service接口
 * <AUTHOR>
 */
public interface SysDistrictService extends BaseService<SysDistrict> {
    /**
     * 初始化国家与省份数据
     */
    void initDistrict();

    /**
     * 获取地区
     * @param response 请求
     */
    List<DistrictItem> getDistrictItems(HttpServletResponse response);
}
