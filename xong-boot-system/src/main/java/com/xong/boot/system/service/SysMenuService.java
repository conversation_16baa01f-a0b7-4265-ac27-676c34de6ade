package com.xong.boot.system.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.service.BaseService;
import com.xong.boot.system.domain.SysMenu;
import com.xong.boot.system.model.req.PageMenuReq;

import java.util.List;
import java.util.Map;

/**
 * 用户菜单 service
 * <AUTHOR>
 */
public interface SysMenuService extends BaseService<SysMenu> {
    /**
     * 获取菜单列表
     * @param queryWrapper 查询构建器
     */
    List<Map<String, Object>> listMenu(QueryWrapper<SysMenu> queryWrapper);

    /**
     * 更新拖拽排序
     * @param params 拖拽数据
     */
    void dropMenu(DropParams params);

    /**
     * 存在菜单编码
     * @param menuCode 菜单编码
     * @param menuId   菜单ID
     */
    void existMenuCode(String menuCode, String menuId);

    /**
     * 存在菜单名称
     * @param name   菜单名称
     * @param menuId 菜单ID
     */
    void existName(String name, String menuId);
}
