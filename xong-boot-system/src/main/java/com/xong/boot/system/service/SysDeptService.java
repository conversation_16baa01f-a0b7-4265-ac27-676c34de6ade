package com.xong.boot.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.service.BaseService;
import com.xong.boot.system.domain.SysDept;

import java.util.List;
import java.util.Map;

/**
 * 部门 service
 * <AUTHOR>
 */
public interface SysDeptService extends BaseService<SysDept> {
    /**
     * 保存部门数据并生成全路径
     * @param dept 部门对象
     */
    boolean saveDept(SysDept dept);

    /**
     * 更新部门数据并修改全路径
     */
    void updateDept(SysDept dept);

    /**
     * 根据部门ID查询部门详情
     * @param deptId 部门ID
     */
    Map<String, Object> getDeptById(String deptId);

    /**
     * 获取部门列表
     * @param queryWrapper 查询参数
     */
    List<Map<String, Object>> listDept(QueryWrapper<SysDept> queryWrapper);

    /**
     * 分页获取已删除部门
     * @param page         分页
     * @param queryWrapper 条件构建器
     */
    Page<SysDept> pageRecycle(Page<SysDept> page, Wrapper<SysDept> queryWrapper);

    /**
     * 还原删除部门
     * @param deptIds 部门ID
     */
    boolean restore(List<String> deptIds);

    /**
     * 更新拖拽排序
     * @param params 拖拽数据
     */
    void dropDept(DropParams params);
}
