package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysDict;
import com.xong.boot.system.domain.SysDictItem;
import com.xong.boot.system.service.SysDictItemService;
import com.xong.boot.system.service.SysDictService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 字典管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/dict")
public class SysDictController extends BaseController<SysDictService, SysDict> {
    private final SysDictItemService sysDictItemService;

    public SysDictController(SysDictItemService sysDictItemService) {
        this.sysDictItemService = sysDictItemService;
    }

    /**
     * 新增字典
     * @param dict 字典参数
     */
    @PostMapping
    @XLog(title = "新增字典", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:dict:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysDict dict) {
        baseService.existDictCode(dict.getDictCode(), null);
        if (baseService.save(dict)) {
            return Result.success("新增字典成功");
        }
        return Result.fail("新增字典失败");
    }

    /**
     * 删除字典
     * @param ids 字典ID
     */
    @DeleteMapping
    @XLog(title = "删除字典", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:dict:delete')")
    public Result delete(@NotEmpty(message = "字典ID不存在") String[] ids) {
        List<String> idList = Arrays.asList(ids);
        long count = sysDictItemService.count(Wrappers.lambdaQuery(SysDictItem.class).in(SysDictItem::getDictId, idList));
        if (count > 0) {
            return Result.fail("存在字典项不允许删除");
        }
        if (baseService.removeByIds(idList)) {
            return Result.success("字典删除成功");
        }
        return Result.fail("字典删除失败");
    }

    /**
     * 编辑字典
     * @param dict 字典参数
     */
    @PutMapping
    @XLog(title = "修改字典", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:dict:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysDict dict) {
        baseService.existDictCode(dict.getDictCode(), dict.getDictId());
        if (baseService.updateById(dict)) {
            return Result.success("字典修改成功");
        }
        return Result.fail("字典修改失败");
    }

    /**
     * 字典详情
     * @param id 字典ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:dict:*')")
    public Result detail(@NotBlank(message = "字典ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 分页获取字典列表
     * @param dict 查询条件
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:dict:list')")
    public Result page(SysDict dict) {
        LambdaQueryWrapper<SysDict> queryWrapper = XQueryWrapper.newInstance(dict)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(SysDict::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}
