package com.xong.boot.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.common.domain.User;
import com.xong.boot.common.mapper.BaseMapper;
import com.xong.boot.framework.domain.items.DeptItem;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.model.resp.UserRoleResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统用户 Mapper
 * <AUTHOR>
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * 查询用户详情
     * @param userId 用户ID
     */
    SysUser selectByUserId(@Param("userId") String userId);

    /**
     * 分页获取部门下的用户
     * @param page   分页
     * @param type   类型 0所有部门用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId 部门ID
     * @param ew     条件构建器
     */
    Page<User> selectPageUser(Page<User> page, @Param("type") Integer type, @Param("deptId") String deptId, @Param(Constants.WRAPPER) Wrapper<User> ew);

    /**
     * 分页获取已删除用户
     * @param page 分页
     * @param ew   条件构建器
     */
    Page<UserItem> selectPageRecycle(Page<UserItem> page, @Param(Constants.WRAPPER) Wrapper<SysUser> ew);

    /**
     * 还原删除账号
     * @param userIds 用户ID
     */
    boolean restoreDelFlag(List<String> userIds);

    /**
     * 分页获取角色用户列表
     * @param page   分页
     * @param userId 用户ID
     * @param type   1包含用户ID的角色 2不包含用户ID的角色
     * @param ew     条件构建起
     */
    Page<UserRoleResp> selectPageUserRole(Page<UserRoleResp> page, @Param("userId") String userId, @Param("type") Integer type, @Param(Constants.WRAPPER) Wrapper<SysRole> ew);

    /**
     * 根据用户ID查询所属部门列表
     * @param userId 用户ID
     */
    List<DeptItem> selectListUserDept(@Param("userId") String userId);

    /**
     * 查询用户账号是否存在
     * @param username 用户账号
     */
    boolean selectExistUsername(@Param("username") String username);

    /**
     * 查询用户编码是否存在
     * @param userCode 用户编码
     * @param userId   排除用户ID
     */
    boolean selectExistUserCode(@Param("userCode") String userCode, @Param("userId") String userId);

    /**
     * 查询用户手机号是否存在
     * @param phone  手机号
     * @param userId 排除用户ID
     */
    boolean selectExistPhone(@Param("phone") String phone, @Param("userId") String userId);

    /**
     * 查询用户身份证号是否存在
     * @param idCard 身份证号
     * @param userId 排除用户ID
     */
    boolean selectExistIdCard(@Param("idCard") String idCard, @Param("userId") String userId);

    /**
     * 查询用户邮箱地址是否存在
     * @param email  邮箱地址
     * @param userId 排除用户ID
     */
    boolean selectExistEmail(@Param("email") String email, @Param("userId") String userId);
}
