package com.xong.boot.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.framework.domain.items.DeptItem;
import com.xong.boot.framework.domain.items.PositionItem;
import com.xong.boot.framework.domain.items.RoleItem;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.system.domain.SysDept;

import java.util.List;

/**
 * 选择控件
 * <AUTHOR>
 */
public interface SelectorService {
    /**
     * 获取部门列表
     * @param queryWrapper 条件构建器
     */
    List<DeptItem> listDept(Wrapper<SysDept> queryWrapper);

    /**
     * 分页获取部门下的用户
     * @param page         分页
     * @param userScope    范围 0所有部门用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId       部门ID
     * @param queryWrapper 条件构建器
     */
    Page<UserItem> pageUser(Page<UserItem> page, Integer userScope, String deptId, Wrapper<?> queryWrapper);

    /**
     * 分页获取角色
     * @param queryWrapper 条件构建器
     */
    List<RoleItem> listRole(Wrapper<?> queryWrapper);

    /**
     * 分页获职务
     * @param queryWrapper 条件构建
     */
    List<PositionItem> listPosition(Wrapper<?> queryWrapper);
}
