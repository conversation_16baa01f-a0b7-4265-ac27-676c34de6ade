package com.xong.boot.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.enums.DistrictLevel;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.lang.Point;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.HttpUtils;
import com.xong.boot.common.utils.ReUtils;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.domain.items.DistrictItem;
import com.xong.boot.framework.properties.AmapProperties;
import com.xong.boot.system.domain.SysDistrict;
import com.xong.boot.system.mapper.SysDistrictMapper;
import com.xong.boot.system.service.SysDistrictService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 地区Service业务层处理
 * <AUTHOR>
 */
@Service
public class SysDistrictServiceImpl extends BaseServiceImpl<SysDistrictMapper, SysDistrict> implements SysDistrictService {
    private final AmapProperties amapProperties;

    public SysDistrictServiceImpl(AmapProperties amapProperties) {
        this.amapProperties = amapProperties;
    }

    /**
     * 初始化国家与省份数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDistrict() {
        String districtUrl = amapProperties.getDistrictUrl();
        if (StringUtils.isBlank(districtUrl)) {
            throw new XServerException("请配置高德地区获取接口地址");
        }
        String amapKey = amapProperties.getAmapKey();
        if (StringUtils.isBlank(amapKey)) {
            throw new XServerException("请配置高德地图KEY");
        }
        List<SysDistrict> districts = null;
        List<SysDistrict> provinceDistricts; // 省级地区
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("key", amapKey);
        paramMap.put("subdistrict", 1);
        try {
            String result = HttpUtils.get(districtUrl, paramMap); // 请求省级区划信息
            JSONObject resultObject = JSON.parseObject(result);
            if (resultObject.getInteger("status") == 1) {
                JSONArray jsonArray = resultObject.getJSONArray("districts");
                provinceDistricts = generateDistrict(jsonArray.getJSONObject(0).getJSONArray("districts"), "");
            } else {
                throw new XServerException("高德接口调用:" + resultObject.getString("info"));
            }
            if (provinceDistricts != null && provinceDistricts.size() > 0) {
                districts = new ArrayList<>(provinceDistricts);
                for (SysDistrict item : provinceDistricts) {
                    paramMap.clear();
                    paramMap.put("key", amapKey);
                    paramMap.put("keywords", item.getName());
                    paramMap.put("subdistrict", 3);
                    String result1 = HttpUtils.get(districtUrl, paramMap);
                    JSONObject resultObject1 = JSON.parseObject(result1);
                    if (resultObject1.getInteger("status") == 1) {
                        JSONArray jsonArray1 = resultObject1.getJSONArray("districts");
                        if (jsonArray1 != null && jsonArray1.size() > 0) {
                            List<SysDistrict> listData = generateDistrict(jsonArray1.getJSONObject(0).getJSONArray("districts"), item.getId());
                            if (listData != null && listData.size() > 0) {
                                districts.addAll(listData);
                            }
                        }
                    } else {
                        throw new XServerException("高德接口调用:" + resultObject1.getString("info"));
                    }
                }
            }
            if (districts != null && districts.size() > 0) {
                saveBatch(districts);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new XServerException("地区数据获取构建失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取地区
     * @param response 请求
     */
    @Override
    public List<DistrictItem> getDistrictItems(HttpServletResponse response) {
        LambdaQueryWrapper<SysDistrict> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByAsc(SysDistrict::getId);
        List<SysDistrict> list = baseMapper.selectList(queryWrapper);
        return list.stream().map(district -> {
            DistrictItem districtItem = new DistrictItem();
            districtItem.setId(district.getId());
            districtItem.setParentId(district.getParentId());
            districtItem.setName(district.getName());
            districtItem.setShortName(district.getShortName());
            districtItem.setLevel(district.getLevel());
            districtItem.setCenter(district.getCenter());
            districtItem.setStatus(district.getStatus());
            return districtItem;
        }).collect(Collectors.toList());
    }

    // 构建区域数据
    private List<SysDistrict> generateDistrict(JSONArray jsonArray, String parentId) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return null;
        }
        List<SysDistrict> listData = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String areaCode = ReUtils.replaceAll(jsonObject.getString("adcode"), "(00)+$", "");
            String cityCode = jsonObject.getString("citycode").replace("[]", "");
            String name = jsonObject.getString("name");
            DistrictLevel districtLevel = DistrictLevel.valueOf(jsonObject.getString("level"));
            String lngLat = jsonObject.getString("center");
            SysDistrict district = new SysDistrict();
            if (districtLevel == DistrictLevel.street) {
                areaCode = areaCode + String.format("%02d", i + 1);
            }
            district.setId(areaCode);
            district.setParentId(parentId);
            district.setCityCode(cityCode);
            district.setName(name);
            district.setShortName(name);
            district.setLevel(districtLevel);
            district.setCenter(new Point(lngLat));
            district.setStatus(0);
            listData.add(district);
            List<SysDistrict> districts = generateDistrict(jsonObject.getJSONArray("districts"), district.getId());
            if (districts != null && districts.size() > 0) {
                listData.addAll(districts);
            }
        }
        return listData;
    }
}
