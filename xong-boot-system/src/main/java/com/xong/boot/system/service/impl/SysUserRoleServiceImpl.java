package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.system.domain.SysUserRole;
import com.xong.boot.system.mapper.SysUserRoleMapper;
import com.xong.boot.system.service.SysUserRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户角色关联 service
 * <AUTHOR>
 */
@Service
public class SysUserRoleServiceImpl extends BaseServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {
    /**
     * 保存用户角色关系
     * @param userRoles 用户角色集
     */
    @Override
    public boolean saveUserRole(List<SysUserRole> userRoles) {
        for (SysUserRole item : userRoles) {
            item.generateId();
        }
        return SqlHelper.retBool(baseMapper.insert(userRoles));
    }
}
