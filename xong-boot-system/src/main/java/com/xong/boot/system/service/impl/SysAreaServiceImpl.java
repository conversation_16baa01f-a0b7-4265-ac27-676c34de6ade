package com.xong.boot.system.service.impl;

import com.xong.boot.system.domain.SysArea;
import com.xong.boot.system.mapper.SysAreaMapper;
import com.xong.boot.system.service.SysAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 区划信息
 *
 * <AUTHOR>
 * @date 2025-7-16
 */
@Service
public class SysAreaServiceImpl implements SysAreaService {
    @Autowired
    private SysAreaMapper sysAreaMapper;

    /**
     * 查询区划信息列表
     *
     * @param sysArea 区划信息
     * @return 区划信息
     */
    @Override
    public List<SysArea> selectSysAreaList(SysArea sysArea) {
        return sysAreaMapper.selectSysAreaList(sysArea);
    }
}


