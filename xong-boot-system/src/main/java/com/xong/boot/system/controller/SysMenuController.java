package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.system.domain.SysMenu;
import com.xong.boot.system.service.SysMenuService;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 菜单管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/menu")
public class SysMenuController extends BaseController<SysMenuService, SysMenu> {
    /**
     * 新增菜单
     * @param menu 菜单对象
     */
    @PostMapping
    @XLog(title = "新增菜单", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:menu:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysMenu menu) {
        baseService.existMenuCode(menu.getMenuCode(), null);
        baseService.existName(menu.getName(), null);
        if (baseService.save(menu)) {
            return Result.success("新增菜单成功");
        }
        return Result.fail("新增菜单失败");
    }

    /**
     * 删除菜单
     * @param ids 菜单ID
     */
    @DeleteMapping
    @XLog(title = "删除菜单", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:menu:delete')")
    public Result delete(@NotNull(message = "菜单ID不存在") String[] ids) {
        long count = baseService.count(Wrappers.lambdaQuery(SysMenu.class)
                .in(SysMenu::getParentId, Arrays.asList(ids)));
        if (count > 0) {
            return Result.fail("存在子菜单不允许删除");
        }
        if (baseService.removeByIds(Arrays.asList(ids))) {
            return Result.success("菜单删除成功");
        }
        return Result.fail("菜单删除失败");
    }

    /**
     * 编辑菜单
     * @param menu 菜单对象
     */
    @PutMapping
    @XLog(title = "修改菜单", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:menu:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysMenu menu) {
        if (menu.getMenuId().equals(menu.getParentId())) {
            return Result.fail("菜单修改失败");
        }
        baseService.existMenuCode(menu.getMenuCode(), menu.getMenuId());
        baseService.existName(menu.getName(), menu.getMenuId());
        if (baseService.updateById(menu)) {
            return Result.success("菜单修改成功");
        }
        return Result.fail("菜单修改失败");
    }

    /**
     * 菜单详情
     * @param id 菜单ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:menu:*')")
    public Result detail(@NotNull(message = "菜单ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 获取菜单列表
     * @param menu 查询条件
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result list(SysMenu menu) {
        QueryWrapper<SysMenu> queryWrapper = XQueryWrapper.newInstance(menu).entity2Condition();
        return Result.successData(baseService.listMenu(queryWrapper));
    }

    /**
     * 菜单拖拽修改 排序
     * @param params 拖拽后参数
     */
    @PutMapping("/drop")
    @PreAuthorize("hasAuthority('system:menu:edit')")
    public Result drop(@RequestBody DropParams params) {
        baseService.dropMenu(params);
        return Result.success("修改成功");
    }
}
