package com.xong.boot.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xong.boot.common.domain.User;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.domain.items.DeptItem;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.framework.domain.items.UserRoleItem;
import com.xong.boot.system.domain.SysDept;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.model.resp.UserRoleResp;

import java.util.List;

/**
 * 系统用户 Service
 * <AUTHOR>
 */
public interface SysUserService extends IService<SysUser> {
    /**
     * 新增用户
     * @param user 用户信息
     */
    String saveUser(SysUser user);

    /**
     * 更新用户信息
     * @param user 用户信息
     */
    void updateUser(SysUser user);

    /**
     * 获取用户详细
     */
    UserDetailsImpl getUser(String userId);

    /**
     * 分页获取用户信息
     * @param page         分页
     * @param type         类型 0所有部门用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId       部门ID
     * @param queryWrapper 条件构建器
     */
    Page<User> pageUser(Page<User> page, Integer type, String deptId, Wrapper<User> queryWrapper);

    /**
     * 账号解锁
     * @param usernames 用户账号集合
     */
    void unlock(List<String> usernames);

    /**
     * 重置密码为默认密码
     * @param userId 用户ID
     * @return 默认密码
     */
    String resetPassword(String userId);

    /**
     * 分页获取已删除用户
     * @param page         分页
     * @param queryWrapper 条件构建器
     */
    Page<UserItem> pageRecycle(Page<UserItem> page, Wrapper<SysUser> queryWrapper);

    /**
     * 还原删除账号
     * @param userIds 用户ID
     */
    boolean restore(List<String> userIds);

    /**
     * 解析默认密码
     * @param username 用户名
     */
    String parseDefaultPassword(String username);

    /**
     * 分页根据角色ID获取用户列表
     * @param page         分页
     * @param userId       用户ID（必须）
     * @param type         1包含用户ID的角色 2不包含用户ID的角色
     * @param queryWrapper 条件构建器
     */
    Page<UserRoleResp> pageUserRole(Page<UserRoleResp> page, String userId, Integer type, Wrapper<SysRole> queryWrapper);

    /**
     * 获取用户部门列表
     * @param userId 用户ID
     */
    List<DeptItem> listUserDept(String userId);

    /**
     * 获取所有用户
     * @return
     */
    List<SysUser> selectAll();
}
