package com.xong.boot.system.controller;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.domain.items.DistrictItem;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.system.domain.SysDistrict;
import com.xong.boot.system.service.SysDistrictService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 地区管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/district")
public class SysDistrictController extends BaseController<SysDistrictService, SysDistrict> {
    /**
     * 新增地区
     * @param district 地区对象
     */
    @PostMapping
    @XLog(title = "新增地区", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:district:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysDistrict district) {
        if (baseService.save(district)) {
            return Result.success("新增地区成功");
        }
        return Result.fail("新增地区失败");
    }

    /**
     * 删除地区
     * @param ids 地区ID
     */
    @DeleteMapping
    @XLog(title = "删除地区", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:district:delete')")
    public Result delete(@NotEmpty(message = "地区ID不存在") String[] ids) {
        List<String> idList = Arrays.asList(ids);
        long count = baseService.count(Wrappers.<SysDistrict>lambdaQuery().in(SysDistrict::getParentId, idList));
        if (count > 0) {
            return Result.fail("存在下级地区不允许删除");
        }
        if (baseService.removeByIds(idList)) {
            return Result.success("地区删除成功");
        }
        return Result.fail("地区删除失败");
    }

    /**
     * 编辑地区
     * @param district 地区对象
     */
    @PutMapping
    @XLog(title = "修改地区", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:district:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysDistrict district) {
        if (baseService.updateById(district)) {
            return Result.success("地区修改成功");
        }
        return Result.fail("地区修改失败");
    }

    /**
     * 获取地区详情
     * @param id 地区ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:district:*')")
    public Result detail(@NotBlank(message = "地区ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 获取地区列表
     * @param district 查询条件
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:district:list')")
    public Result list(SysDistrict district) {
        LambdaQueryWrapper<SysDistrict> queryWrapper = XQueryWrapper.newInstance(district).lambda();
        queryWrapper.orderByAsc(SysDistrict::getId);
        return Result.successData(baseService.list(queryWrapper));
    }

    /**
     * 地区资源文件下载
     */
    @GetMapping("/download")
    @PreAuthorize("hasAuthority('system:district:download')")
    public void download(HttpServletResponse response) throws IOException {
        List<DistrictItem> districtItems = baseService.getDistrictItems(response);
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("地区数据.json", StandardCharsets.UTF_8));
        IoUtil.writeUtf8(response.getOutputStream(), true, JSON.toJSONString(districtItems));
    }
}
