package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.service.SysRoleService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 角色管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/role")
public class SysRoleController extends BaseController<SysRoleService, SysRole> {
    /**
     * 新增角色
     * @param params 角色信息
     */
    @PostMapping
    @XLog(title = "新增角色", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysRole params) {
        baseService.existRoleCode(params.getRoleCode(), null);
        baseService.save(params);
        return Result.success("新增角色成功");
    }

    /**
     * 删除角色
     * @param ids 角色ID
     */
    @DeleteMapping
    @XLog(title = "删除角色", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:role:delete')")
    public Result delete(@NotEmpty(message = "角色ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        baseService.existRoleUser(list);
        baseService.removeByIds(list);
        return Result.success("角色删除成功");
    }

    /**
     * 编辑角色
     * @param params 角色信息
     */
    @PutMapping
    @XLog(title = "修改角色", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysRole params) {
        baseService.existRoleCode(params.getRoleCode(), params.getRoleId());
        baseService.updateById(params);
        return Result.success("角色修改成功");
    }

    /**
     * 获取角色详情
     * @param id 角色ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:role:*')")
    public Result detail(@NotBlank(message = "角色ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 分页获取角色
     * @param role 查询条件
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result page(SysRole role) {
        LambdaQueryWrapper<SysRole> queryWrapper = XQueryWrapper.newInstance(role)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(SysRole::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 分页获取角色用户列表
     * @param type   1包含角色ID的用户 2不包含角色ID的用户
     * @param roleId 角色ID （必须）
     * @param user   查询参数
     */
    @GetMapping("/page/user")
    @PreAuthorize("hasAuthority('system:user:role:list')")
    public Result pageRoleUser(@NotNull(message = "获取类型错误") Integer type, @NotBlank(message = "角色ID不存在") String roleId, SysUser user) {
        QueryWrapper<SysUser> queryWrapper = XQueryWrapper.newInstance(user)
                .entity2Condition()
                .startAdvancedQuery()
                .startSort();
        return Result.successData(baseService.pageRoleUser(QueryHelper.getPage(), roleId, type, queryWrapper));
    }
}
