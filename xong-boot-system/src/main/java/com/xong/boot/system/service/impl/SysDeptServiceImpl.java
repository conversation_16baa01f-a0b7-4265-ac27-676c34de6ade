package com.xong.boot.system.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.common.enums.DropPosition;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.system.domain.SysDept;
import com.xong.boot.system.mapper.SysDeptMapper;
import com.xong.boot.system.service.SysDeptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 部门 service
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl extends BaseServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {

    /**
     * 保存部门数据并生成全路径
     * @param dept 部门对象
     */
    @Override
    public boolean saveDept(SysDept dept) {
        if (existDeptCode(dept.getDeptCode(), null)) {
            throw new XServerException("部门编码已存在");
        }
        List<String> fullPaths = new ArrayList<>(); // 构建全路径
        if (StringUtils.isBlank(dept.getParentId())) {
            fullPaths.add("");
        } else {
            SysDept parentDept = getById(dept.getParentId());
            fullPaths.addAll(parentDept.getFullPath());
        }
        dept.setDeptId(IdUtil.getSnowflakeNextIdStr());
        fullPaths.add(dept.getDeptId());
        dept.setFullPath(fullPaths);
        return save(dept);
    }

    /**
     * 更新部门数据并修改全路径
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDept(SysDept dept) {
        if (existDeptCode(dept.getDeptCode(), dept.getDeptId())) {
            throw new XServerException("部门编码已存在");
        }
        updateDeptAndFullPath(dept);
    }

    /**
     * 根据部门ID查询部门详情
     * @param deptId 部门ID
     */
    @Override
    public Map<String, Object> getDeptById(String deptId) {
        return baseMapper.selectDeptById(deptId);
    }

    /**
     * 获取部门列表
     * @param queryWrapper 查询参数
     */
    @Override
    public List<Map<String, Object>> listDept(QueryWrapper<SysDept> queryWrapper) {
        return baseMapper.selectListDept(queryWrapper);
    }

    /**
     * 分页获取已删除部门
     * @param page         分页
     * @param queryWrapper 条件构建器
     */
    @Override
    public Page<SysDept> pageRecycle(Page<SysDept> page, Wrapper<SysDept> queryWrapper) {
        return baseMapper.selectPageRecycle(page, queryWrapper);
    }

    /**
     * 还原删除部门
     * @param deptIds 部门ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean restore(List<String> deptIds) {
        // 把所有删除部门还原
        baseMapper.restoreDelFlag(deptIds);
        // 获取没有父级的部门ID
        List<String> notParentDeptIds = baseMapper.selectNotParentDeptId(deptIds);
        if (notParentDeptIds == null || notParentDeptIds.size() == 0) {
            return true;
        }
        notParentDeptIds.forEach(id -> {
            LambdaUpdateWrapper<SysDept> updateWrapper = Wrappers.<SysDept>lambdaUpdate()
                    .in(SysDept::getDeptId, notParentDeptIds)
                    .set(SysDept::getParentId, "")
                    .set(SysDept::getFullPath, "," + id);
            baseMapper.update(updateWrapper);
        });
        return true;
    }

    /**
     * 更新拖拽排序
     * @param params 拖拽数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dropDept(DropParams params) {
        // 获取放置节点数据
        SysDept dropDept = getById(params.getDropId());
        // 获取放置节点同级数据
        List<SysDept> peerDepts;
        if (params.getPosition() == DropPosition.LOWER) { // 放置节点下
            peerDepts = list(Wrappers.lambdaQuery(SysDept.class)
                    .eq(SysDept::getParentId, dropDept.getDeptId())
                    .ne(SysDept::getDeptId, params.getDragId())
                    .orderByAsc(SysDept::getSortOn));
        } else {
            peerDepts = list(Wrappers.lambdaQuery(SysDept.class)
                    .eq(SysDept::getParentId, dropDept.getParentId())
                    .ne(SysDept::getDeptId, params.getDragId())
                    .orderByAsc(SysDept::getSortOn));
        }
        List<SysDept> updateDepts = new ArrayList<>();
        if (params.getPosition() == DropPosition.LOWER) { // 放置节下
            AtomicInteger i = new AtomicInteger();
            SysDept firstDept = new SysDept();
            firstDept.setDeptId(params.getDragId());
            firstDept.setParentId(params.getDropId());
            firstDept.setSortOn(i.incrementAndGet());
            updateDeptAndFullPath(firstDept);
            if (peerDepts != null && peerDepts.size() > 0) {
                peerDepts.forEach(m -> {
                    SysDept dept = new SysDept();
                    dept.setParentId(m.getParentId());
                    dept.setDeptId(m.getDeptId());
                    dept.setSortOn(i.incrementAndGet());
                    updateDepts.add(dept);
                });
            }
        } else if (params.getPosition() == DropPosition.AFTER) { // 放置节点后
            AtomicInteger i = new AtomicInteger();
            peerDepts.forEach(m -> {
                SysDept dept = new SysDept();
                dept.setParentId(m.getParentId());
                dept.setDeptId(m.getDeptId());
                dept.setSortOn(i.incrementAndGet());
                updateDepts.add(dept);
                // 如果是当前是放置节点，就在后面添加拖拽节点
                if (params.getDropId().equals(m.getDeptId())) {
                    SysDept dragDept = new SysDept();
                    dragDept.setDeptId(params.getDragId());
                    dragDept.setParentId(m.getParentId());
                    dragDept.setSortOn(i.incrementAndGet());
                    updateDeptAndFullPath(dragDept);
                }
            });
        } else { // 放置节点前
            AtomicInteger i = new AtomicInteger();
            SysDept firstDept = new SysDept();
            firstDept.setDeptId(params.getDragId());
            firstDept.setParentId(dropDept.getParentId());
            firstDept.setSortOn(i.incrementAndGet());
            updateDeptAndFullPath(firstDept);
            peerDepts.forEach(m -> {
                SysDept dept = new SysDept();
                dept.setParentId(m.getParentId());
                dept.setDeptId(m.getDeptId());
                dept.setSortOn(i.incrementAndGet());
                updateDepts.add(dept);
            });
        }
        if (updateDepts.size() > 0) {
            updateBatchById(updateDepts);
        }
    }

    /**
     * 更新部门信息与部门全路径
     * @param dept 更新部门
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDeptAndFullPath(SysDept dept) {
        SysDept oldDept = getById(dept.getDeptId());
        if (dept.getParentId() == null || dept.getParentId().equals(oldDept.getParentId())) { // 判断是否进行了级别调整
            if (!updateById(dept)) {
                throw new XServerException("部门更新失败");
            }
            return;
        }
        // 如修改了父级组织，则修改关联的所有下级组织
        List<String> fullPath = new ArrayList<>(); // 构建当前组织新的全路径
        if (StringUtils.isBlank(dept.getParentId())) {
            fullPath.add("");
        } else {
            SysDept superDept = getById(dept.getParentId());
            fullPath.addAll(superDept.getFullPath());
        }
        fullPath.add(dept.getDeptId());
        dept.setFullPath(fullPath);
        if (updateById(dept)) {
            String rootPath = StringUtils.join(",", fullPath);
            String oldPath = StringUtils.join(",", oldDept.getFullPath());
            LambdaQueryWrapper<SysDept> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(SysDept::getDeptId, SysDept::getFullPath);
            queryWrapper.likeRight(SysDept::getFullPath, oldPath + ",");
            List<SysDept> deptList = list(queryWrapper); // 获取包含旧节点的所有组织
            if (deptList == null || deptList.size() == 0) {
                return;
            }
            deptList.forEach(d -> {
                String nowFullPath = StringUtils.join(",", d.getFullPath()).replace(oldPath, rootPath);
                d.setFullPath(Arrays.asList(nowFullPath.split(","))); // 替换父级路径
            });
            if (!updateBatchById(deptList)) { // 更新所有下级全路径
                throw new XServerException("更新下级全路径失败");
            }
        } else {
            throw new XServerException("部门更新失败");
        }
    }

    /**
     * 存在部门编码
     * @param deptCode 部门编码
     * @param deptId   部门ID
     */
    private boolean existDeptCode(String deptCode, String deptId) {
        if (deptCode == null) {
            return false;
        }
        LambdaQueryWrapper<SysDept> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysDept::getDeptCode, deptCode);
        if (StringUtils.isNotBlank(deptId)) {
            queryWrapper.ne(SysDept::getDeptId, deptId);
        }
        long count = count(queryWrapper);
        return count > 0;
    }

    /**
     * 按部门ID查询部门以及子部门信息
     * @param rootDeptId 部门ID
     * @return 部门及子部门列表
     */
    public List<SysDept> selectDeptAndChildList(String rootDeptId) {
        if (StringUtils.isBlank(rootDeptId)) {
            return Collections.emptyList();
        }
        SysDept rootDept = baseMapper.selectById(rootDeptId);
        if(rootDept != null){
            return Collections.emptyList();
        }
        // 使用 fullPath 字段匹配所有包含 rootDeptId 的子部门
        LambdaQueryWrapper<SysDept> queryWrapper = Wrappers.lambdaQuery(SysDept.class)
                .likeRight(SysDept::getFullPath, rootDept.getFullPath()); // 右侧模糊匹配全路径，获取所有子节点
        return list(queryWrapper);
    }
}
