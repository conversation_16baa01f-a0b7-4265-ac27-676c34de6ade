package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.framework.domain.items.DeptItem;
import com.xong.boot.framework.domain.items.PositionItem;
import com.xong.boot.framework.domain.items.RoleItem;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.system.domain.SysDept;
import com.xong.boot.system.mapper.SelectorMapper;
import com.xong.boot.system.service.SelectorService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 选择控件
 * <AUTHOR>
 */
@Service
public class SelectorServiceImpl implements SelectorService {
    private final SelectorMapper selectorMapper;

    public SelectorServiceImpl(SelectorMapper selectorMapper) {
        this.selectorMapper = selectorMapper;
    }

    /**
     * 获取部门列表
     * @param queryWrapper 条件构建器
     */
    @Override
    public List<DeptItem> listDept(Wrapper<SysDept> queryWrapper) {
        return selectorMapper.listDept(queryWrapper);
    }

    /**
     * 分页获取部门下的用户
     * @param page         分页
     * @param userScope    范围 0所有部门用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId       部门ID
     * @param queryWrapper 条件构建器
     */
    @Override
    public Page<UserItem> pageUser(Page<UserItem> page, Integer userScope, String deptId, Wrapper<?> queryWrapper) {
        return selectorMapper.pageUser(page, userScope, deptId, queryWrapper);
    }

    @Override
    public List<RoleItem> listRole(Wrapper<?> queryWrapper) {
        return selectorMapper.listRole(queryWrapper);
    }

    @Override
    public List<PositionItem> listPosition(Wrapper<?> queryWrapper) {
        return selectorMapper.listPosition(queryWrapper);
    }
}
