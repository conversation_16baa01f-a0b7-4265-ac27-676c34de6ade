package com.xong.boot.system.service.impl;

import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.system.domain.SysRule;
import com.xong.boot.system.mapper.SysRuleMapper;
import com.xong.boot.system.service.SysRuleService;
import org.springframework.stereotype.Service;

/**
 * 数据规则 service
 * <AUTHOR>
 */
@Service
public class SysRuleServiceImpl extends BaseServiceImpl<SysRuleMapper, SysRule> implements SysRuleService {
    /**
     * 规则是否在角色菜单关系中
     * @param ruleId 规则ID
     */
    @Override
    public boolean existRoleMenu(String ruleId) {
        return baseMapper.existRoleMenu(ruleId);
    }
}
