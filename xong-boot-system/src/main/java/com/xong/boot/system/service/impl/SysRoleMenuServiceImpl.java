package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.system.domain.SysRoleMenu;
import com.xong.boot.system.mapper.SysRoleMenuMapper;
import com.xong.boot.system.service.SysRoleMenuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色菜单
 * <AUTHOR>
 */
@Service
public class SysRoleMenuServiceImpl extends BaseServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {
    /**
     * 根据角色ID更新角色菜单
     * @param roleId  角色ID
     * @param menuIds 菜单ID集
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleMenuByRoleId(String roleId, List<String> menuIds) {
        if (StringUtils.isBlank(roleId)) {
            throw new XServerException("角色ID不存在");
        }
        if (menuIds == null || menuIds.size() == 0) {
            throw new XServerException("菜单ID不存在");
        }
        baseMapper.delete(Wrappers.<SysRoleMenu>lambdaQuery().eq(SysRoleMenu::getRoleId, roleId));
        List<SysRoleMenu> listData = new ArrayList<>();
        for (String menuId : menuIds) {
            SysRoleMenu roleMenu = new SysRoleMenu();
            roleMenu.setRoleId(roleId);
            roleMenu.setMenuId(menuId);
            roleMenu.generateId();
            listData.add(roleMenu);
        }
        return SqlHelper.retBool(baseMapper.insert(listData));
    }
}
