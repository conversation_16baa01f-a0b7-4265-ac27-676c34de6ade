package com.xong.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.file.PathUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.crypto.file.DelegatingFileEncoder;
import com.xong.boot.common.crypto.password.PasswordEncoder;
import com.xong.boot.common.domain.User;
import com.xong.boot.common.exception.XFileException;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.lang.UserAvatar;
import com.xong.boot.common.model.PathRuleIds;
import com.xong.boot.common.properties.FileProperties;
import com.xong.boot.common.utils.FileNameUtils;
import com.xong.boot.common.utils.LocalDateTimeUtils;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.domain.items.*;
import com.xong.boot.framework.model.GlobalConfig;
import com.xong.boot.framework.service.GlobalConfigService;
import com.xong.boot.framework.service.UserDetailsService;
import com.xong.boot.framework.utils.SecurityUtils;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.mapper.SysDistrictMapper;
import com.xong.boot.system.mapper.UserDetailsMapper;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 获取用户信息
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl extends UserDetailsService {
    private final UserDetailsMapper userDetailsMapper;
    private final SysDistrictMapper districtMapper;
    private final GlobalConfigService globalConfigService;
    private final PasswordEncoder passwordEncoder;
    private final FileProperties fileProperties;
    private final DelegatingFileEncoder delegatingFileEncoder;

    public UserDetailsServiceImpl(UserDetailsMapper userDetailsMapper,
                                  SysDistrictMapper districtMapper,
                                  GlobalConfigService globalConfigService,
                                  PasswordEncoder passwordEncoder,
                                  FileProperties fileProperties,
                                  DelegatingFileEncoder delegatingFileEncoder) {
        this.userDetailsMapper = userDetailsMapper;
        this.districtMapper = districtMapper;
        this.globalConfigService = globalConfigService;
        this.passwordEncoder = passwordEncoder;
        this.fileProperties = fileProperties;
        this.delegatingFileEncoder = delegatingFileEncoder;
    }

    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     */
    @Override
    public UserDetailsImpl loadUserByUserId(String userId) {
        User user = userDetailsMapper.selectByUserId(userId);
        return BeanUtil.copyProperties(user, UserDetailsImpl.class);
    }

    /**
     * 根据账号获取用户信息
     * @param username 账号
     */
    @Override
    public UserDetailsImpl loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userDetailsMapper.selectByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在");
        }
        return BeanUtil.copyProperties(user, UserDetailsImpl.class);
    }

    /**
     * 获取当前部门
     * @param deptId 部门ID
     */
    @Override
    public DeptItem getDept(String deptId) {
        if (StringUtils.isNotBlank(deptId)) {
            return userDetailsMapper.selectDeptById(deptId);
        }
        return null;
    }

    /**
     * 获取用户所属部门
     * @param userId 用户ID
     */
    @Override
    public List<DeptItem> getDepts(String userId) {
        return userDetailsMapper.selectDeptByUserId(userId);
    }

    /**
     * 获取地址
     * @param districtIds 地区ID集
     */
    @Override
    public List<DistrictItem> getDistricts(List<String> districtIds) {
        if (districtIds != null && districtIds.size() > 0) {
            return districtMapper.selectDistricts(String.join(",", districtIds));
        }
        return null;
    }

    /**
     * 获取用户职务
     * @param userId 用户ID
     */
    @Override
    public List<PositionItem> getPositions(String userId) {
        return userDetailsMapper.selectPositionByUserId(userId);
    }

    /**
     * 获取用户角色
     * @param userId 用户ID
     * @param deptId 用户当前部门ID
     */
    @Override
    public List<RoleItem> getRoles(String userId, String deptId) {
        return userDetailsMapper.selectRole(userId, deptId);
    }

    /**
     * 获取用户权限
     * @param userId 用户ID
     * @param deptId 用户当前部门ID
     */
    @Override
    public List<PermissionItem> getPermissions(String userId, String deptId) {
        return userDetailsMapper.selectPermission(userId, deptId);
    }

    /**
     * 获取用户菜单
     * @param userId 用户ID
     * @param deptId 用户当前部门ID
     */
    @Override
    public List<MenuItem> getMenus(String userId, String deptId) {
        return userDetailsMapper.selectMenu(userId, deptId);
    }

    /**
     * 获取用户数据规则
     * @param userId 用户ID
     * @param deptId 用户当前部门ID
     */
    @Override
    public List<RuleItem> getRules(String userId, String deptId) {
        List<PathRuleIds> pathRuleIds = userDetailsMapper.selectPathRuleIds(userId, deptId);
        if (pathRuleIds == null || pathRuleIds.size() == 0) {
            return null;
        }
        List<RuleItem> ruleItems = new ArrayList<>();
        for (PathRuleIds pathRules : pathRuleIds) {
            String[] ids = pathRules.getRuleIds().split(",");
            if (ids.length == 0) {
                continue;
            }
            List<RuleItem> items = userDetailsMapper.selectRule(ids);
            if (items == null || items.size() == 0) {
                continue;
            }
            for (RuleItem rule : items) {
                rule.setPath(pathRules.getPath());
                ruleItems.add(rule);
            }
        }
        if (ruleItems.size() == 0) {
            return null;
        }
        Map<String, RuleItem> uniqueRuleItems = new HashMap<>();
        for (RuleItem item : ruleItems) {
            uniqueRuleItems.put(item.getRuleId() + item.getPath(), item);
        }
        return new ArrayList<>(uniqueRuleItems.values());
    }

    /**
     * 更新个人信息
     * @param user 用户信息
     */
    @Override
    public void updateUser(User user) {
        LambdaUpdateWrapper<SysUser> updateWrapper = Wrappers.<SysUser>lambdaUpdate()
                .eq(SysUser::getUserId, user.getUserId())
                .set(SysUser::getRealname, user.getRealname())
                .set(SysUser::getPhone, user.getPhone())
                .set(SysUser::getIdCard, user.getIdCard())
                .set(SysUser::getEmail, user.getEmail())
                .set(SysUser::getGender, user.getGender())
                .set(SysUser::getBirthday, user.getBirthday());
        userDetailsMapper.update(updateWrapper);
    }

    /**
     * 更新密码
     * @param newPassword 新密码
     * @param oldPassword 旧密码
     * @param username    用户账号
     */
    @Override
    public Result updatePassword(String newPassword, String oldPassword, String username) {
        if (StringUtils.isBlank(username)) {
            return Result.fail("密码修改失败");
        }
        GlobalConfig globalConfig = globalConfigService.getGlobalConfig();
        SecurityUtils.checkPasswordComplexity(newPassword, globalConfig);
        User user = userDetailsMapper.selectByUsername(username);
        if (user == null) {
            return Result.fail("账号不存在");
        }
        if (user.getDelFlag() || user.getStatus() != 0) {
            return Result.fail("账号已被删除或禁用");
        }
        if (!passwordEncoder.matches(oldPassword, user.getPassword(), user.getSalt())) {
            return Result.fail("密码错误");
        }
        String password = passwordEncoder.encode(newPassword, user.getSalt());
        // 重置密码过期时间
        LocalDateTime expiredTime = LocalDateTime.now().plusSeconds(globalConfig.getPasswordExpireTime());
        userDetailsMapper.update(Wrappers.<SysUser>lambdaUpdate()
                .eq(SysUser::getUsername, username)
                .set(SysUser::getPassword, password)
                .set(SysUser::getCredentialsExpired, LocalDateTimeUtils.toEpochSecond(expiredTime)));
        return Result.success("密码修改成功");
    }

    /**
     * 上传头像
     * @param userId 用户ID
     * @param file   头像
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserAvatar uploadAvatar(String userId, MultipartFile file) throws IOException {
        if (StringUtils.isBlank(userId)) {
            throw new XServerException("用户ID不存在");
        }
        if (file == null || file.isEmpty()) { // 判断是否有头像
            throw new XFileException("头像不存在");
        }
        String[] allowSuffix = fileProperties.getAvatarAllowSuffix();
        String suffix = FileNameUtils.extName(file.getOriginalFilename());
        DataSize maxSize = fileProperties.getAvatarMaxSize();
        long size = file.getSize();
        if (!FileNameUtils.isType(allowSuffix, suffix)) {
            throw new XFileException(String.format("只能上传%s格式头像", Arrays.toString(allowSuffix)));
        }
        if (size > maxSize.toBytes()) {
            throw new XFileException(String.format("文件大小请控制在%dM以内", maxSize.toMegabytes()));
        }
        UserAvatar userAvatar = new UserAvatar();
        userAvatar.setUserId(userId);
        userAvatar.setContentType(file.getContentType());
        userAvatar.setSuffix(suffix);
        userAvatar.setSize(size);
        userAvatar.setAlgorithm(fileProperties.getAlgorithm());
        userAvatar.setUploadTime(LocalDateTimeUtils.getEpochSecond());
        try (InputStream is = file.getInputStream()) {
            Path path = fileProperties.getAvatarAbsolutePath(userId);
            PathUtil.mkParentDirs(path);
            delegatingFileEncoder.encode(is, PathUtil.getOutputStream(path));
            userAvatar.setRelativePath(fileProperties.absoluteToRelativePath(path.toString()));
        }
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setAvatar(userAvatar);
        if (!SqlHelper.retBool(userDetailsMapper.updateById(sysUser))) {
            throw new XFileException("头像上传失败");
        }
        return userAvatar;
    }

    /**
     * 切换个人部门
     * @param userId 用户ID
     * @param deptId 部门ID
     */
    @Override
    public void switchDept(String userId, String deptId) {
        if (StringUtils.isBlank(userId)) {
            throw new XServerException("用户ID不存在");
        }
        userDetailsMapper.update(Wrappers
                .<SysUser>lambdaUpdate()
                .eq(SysUser::getUserId, userId)
                .set(SysUser::getDeptId, deptId));
    }
}
