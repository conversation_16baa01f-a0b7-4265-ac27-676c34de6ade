package com.xong.boot.system.service;

import com.xong.boot.common.service.BaseService;
import com.xong.boot.system.domain.SysRoleMenu;

import java.util.List;

/**
 * 角色菜单
 * <AUTHOR>
 */
public interface SysRoleMenuService extends BaseService<SysRoleMenu> {
    /**
     * 根据角色ID更新角色菜单
     * @param roleId  角色ID
     * @param menuIds 菜单ID集
     */
    boolean updateRoleMenuByRoleId(String roleId, List<String> menuIds);
}
