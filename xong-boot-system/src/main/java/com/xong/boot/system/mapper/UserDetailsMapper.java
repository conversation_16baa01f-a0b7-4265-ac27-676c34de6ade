package com.xong.boot.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xong.boot.common.domain.User;
import com.xong.boot.common.model.PathRuleIds;
import com.xong.boot.framework.domain.items.*;
import com.xong.boot.system.domain.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户详情
 * <AUTHOR>
 */
@Mapper
public interface UserDetailsMapper extends BaseMapper<SysUser> {
    /**
     * 根据用户ID查询用户详情
     * @param userId 用户ID
     */
    User selectByUserId(@Param("userId") String userId);

    /**
     * 根据账号查询用户详情
     * @param username 账号
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据ID查询部门
     * @param deptId 部门ID
     */
    DeptItem selectDeptById(@Param("deptId") String deptId);

    /**
     * 根据用户ID查询部门
     * @param userId 用户ID
     */
    List<DeptItem> selectDeptByUserId(@Param("userId") String userId);

    /**
     * 根据地区编码查询地区
     * @param id 地区ID
     */
    DistrictItem selectDistrictById(@Param("id") String id);

    /**
     * 根据用户ID查询地区
     * @param userId 用户ID
     */
    List<PositionItem> selectPositionByUserId(@Param("userId") String userId);

    /**
     * 查询用户角色
     * @param userId 用户ID
     * @param deptId 部门ID
     */
    List<RoleItem> selectRole(@Param("userId") String userId, @Param("deptId") String deptId);

    /**
     * 查询用户权限
     * @param userId 用户ID
     * @param deptId 部门ID
     */
    List<PermissionItem> selectPermission(@Param("userId") String userId, @Param("deptId") String deptId);

    /**
     * 查询用户菜单
     * @param userId 用户ID
     * @param deptId 部门ID
     */
    List<MenuItem> selectMenu(@Param("userId") String userId, @Param("deptId") String deptId);

    /**
     * 查询用户数据规则
     * @param ruleIds 规则ID集
     */
    List<RuleItem> selectRule(@Param("ruleIds") String[] ruleIds);

    /**
     * 查询用户相关接口数据规则
     * @param userId 用户ID
     * @param deptId 部门ID
     */
    List<PathRuleIds> selectPathRuleIds(@Param("userId") String userId, @Param("deptId") String deptId);
}
