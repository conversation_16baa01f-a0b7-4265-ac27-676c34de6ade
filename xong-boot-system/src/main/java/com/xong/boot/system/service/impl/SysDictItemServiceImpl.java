package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.system.domain.SysDictItem;
import com.xong.boot.system.mapper.SysDictItemMapper;
import com.xong.boot.system.service.SysDictItemService;
import org.springframework.stereotype.Service;

/**
 * 字典项 service
 * <AUTHOR>
 */
@Service
public class SysDictItemServiceImpl extends BaseServiceImpl<SysDictItemMapper, SysDictItem> implements SysDictItemService {
    /**
     * 验证字典值
     * @param dictId 字段ID
     * @param value  字典值
     * @param id     字典项ID
     */
    @Override
    public void checkValue(String dictId, String value, String id) {
        LambdaQueryWrapper<SysDictItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysDictItem::getDictId, dictId);
        queryWrapper.eq(SysDictItem::getValue, value);
        if (StringUtils.isNotBlank(id)) {
            queryWrapper.ne(SysDictItem::getId, id);
        }
        long count = count(queryWrapper);
        if (count > 0) {
            throw new XServerException("字典值已存在");
        }
    }
}
