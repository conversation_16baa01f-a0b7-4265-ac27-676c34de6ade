package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xong.boot.common.annotation.DataScope;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysPosition;
import com.xong.boot.system.service.SysPositionService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 职务管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/position")
public class SysPositionController extends BaseController<SysPositionService, SysPosition> {
    /**
     * 新增职务
     * @param position 职务对象
     */
    @PostMapping
    @XLog(title = "新增职务", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:position:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysPosition position) {
        if (baseService.save(position)) {
            return Result.success("职务新增成功");
        }
        return Result.fail("职务新增失败");
    }

    /**
     * 删除职务
     * @param ids 职务ID
     */
    @DeleteMapping
    @XLog(title = "删除职务", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:position:delete')")
    public Result delete(@NotEmpty(message = "职务ID不存在") String[] ids) {
        if (baseService.removeByIds(Arrays.asList(ids))) {
            return Result.success("职务删除成功");
        }
        return Result.fail("职务删除失败");
    }

    /**
     * 编辑职务
     * @param position 职务对象
     */
    @PutMapping
    @XLog(title = "修改职务", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:position:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysPosition position) {
        if (baseService.updateById(position)) {
            return Result.success("职务修改成功");
        }
        return Result.fail("职务修改失败");
    }

    /**
     * 职务详情
     * @param id 职务ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:position:*')")
    public Result detail(@NotBlank(message = "职务ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 分页获取职务列表
     * @param params 查询条件
     */
    @GetMapping("/page")
    @DataScope
    @PreAuthorize("hasAuthority('system:position:list')")
    public Result page(SysPosition params) {
        LambdaQueryWrapper<SysPosition> queryWrapper = XQueryWrapper.newInstance(params)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByAsc(SysPosition::getSortOn);
        queryWrapper.orderByAsc(SysPosition::getPostLevel);
        queryWrapper.orderByDesc(SysPosition::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }
}
