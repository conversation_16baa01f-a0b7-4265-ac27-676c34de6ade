package com.xong.boot.system.controller;

import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.framework.model.GlobalConfig;
import com.xong.boot.framework.service.GlobalConfigService;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统配置
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/config")
public class SysConfigController {
    private final GlobalConfigService globalConfigService;

    public SysConfigController(GlobalConfigService globalConfigService) {
        this.globalConfigService = globalConfigService;
    }

    /**
     * 获取系统配置
     */
    @GetMapping
    @PreAuthorize("hasAuthority('system:config:list')")
    public Result detail() {
        return Result.successData(globalConfigService.getGlobalConfig());
    }

    /**
     * 修改系统配置
     * @param params 系统配置参数
     */
    @Validated
    @PutMapping
    @PreAuthorize("hasAuthority('system:config:edit')")
    public Result edit(@NotNull(message = "配置不存在") @RequestBody GlobalConfig params) {
        if (globalConfigService.updateGlobalConfig(params)) {
            return Result.success("修改成功");
        }
        return Result.fail("修改失败");
    }

    /**
     * 重置系统配置
     */
    @PutMapping("/reset")
    @PreAuthorize("hasAuthority('system:config:edit')")
    public Result reset() {
        globalConfigService.resetGlobalConfig();
        return Result.success("重置成功");
    }
}
