package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.system.domain.SysDict;
import com.xong.boot.system.mapper.SysDictMapper;
import com.xong.boot.system.service.SysDictService;
import org.springframework.stereotype.Service;

/**
 * 字典 service
 * <AUTHOR>
 */
@Service
public class SysDictServiceImpl extends BaseServiceImpl<SysDictMapper, SysDict> implements SysDictService {
    /**
     * 存在字典编码
     * @param dictCode 字典编码
     * @param dictId   字典ID
     */
    public void existDictCode(String dictCode, String dictId) {
        LambdaQueryWrapper<SysDict> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysDict::getDictCode, dictCode);
        if (StringUtils.isNotBlank(dictId)) {
            queryWrapper.ne(SysDict::getDictId, dictId);
        }
        long count = count(queryWrapper);
        if (count > 0) {
            throw new XServerException("字典编码已存在");
        }
    }
}
