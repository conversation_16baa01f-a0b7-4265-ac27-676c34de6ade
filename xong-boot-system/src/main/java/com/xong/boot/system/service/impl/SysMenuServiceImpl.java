package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.enums.DropPosition;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.model.DropParams;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.system.domain.SysMenu;
import com.xong.boot.system.mapper.SysMenuMapper;
import com.xong.boot.system.model.req.PageMenuReq;
import com.xong.boot.system.service.SysMenuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 用户菜单 service
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl extends BaseServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {
    @Override
    public List<Map<String, Object>> listMenu(QueryWrapper<SysMenu> queryWrapper) {
        return baseMapper.selectListMenu(queryWrapper);
    }

    /**
     * 更新拖拽排序
     * @param params 拖拽数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dropMenu(DropParams params) {
        // 获取放置节点数据
        SysMenu dropMenu = getById(params.getDropId());
        // 获取拖拽节点数据
        SysMenu dragMenu = getById(params.getDragId());
        // 获取放置节点同级数据
        List<SysMenu> peerMenus;
        if (params.getPosition() == DropPosition.LOWER) { // 放置节点下
            peerMenus = list(Wrappers.lambdaQuery(SysMenu.class)
                    .eq(SysMenu::getParentId, dropMenu.getMenuId())
                    .ne(SysMenu::getMenuId, params.getDragId())
                    .orderByAsc(SysMenu::getSortOn));
        } else {
            peerMenus = list(Wrappers.lambdaQuery(SysMenu.class)
                    .eq(SysMenu::getParentId, dropMenu.getParentId())
                    .ne(SysMenu::getMenuId, params.getDragId())
                    .orderByAsc(SysMenu::getSortOn));
        }
        List<SysMenu> updateMenus = new ArrayList<>();
        if (params.getPosition() == DropPosition.LOWER) { // 放置节下
            AtomicInteger i = new AtomicInteger();
            updateMenus.add(toMenu(dropMenu.getMenuId(), dragMenu, i.incrementAndGet()));
            if (peerMenus != null && peerMenus.size() > 0) {
                peerMenus.forEach(m -> updateMenus.add(toMenu(m.getParentId(), m, i.incrementAndGet())));
            }
        } else if (params.getPosition() == DropPosition.AFTER) { // 放置节点后
            AtomicInteger i = new AtomicInteger();
            peerMenus.forEach(m -> {
                updateMenus.add(toMenu(m.getParentId(), m, i.incrementAndGet()));
                // 如果是当前是放置节点，就在后面添加拖拽节点
                if (dropMenu.getMenuId().equals(m.getMenuId())) {
                    updateMenus.add(toMenu(dropMenu.getParentId(), dragMenu, i.incrementAndGet()));
                }
            });
        } else { // 放置节点前
            AtomicInteger i = new AtomicInteger();
            updateMenus.add(toMenu(dropMenu.getParentId(), dragMenu, i.incrementAndGet()));
            peerMenus.forEach(m -> updateMenus.add(toMenu(m.getParentId(), m, i.incrementAndGet())));
        }
        updateBatchById(updateMenus);
    }

    private SysMenu toMenu(String parentId, SysMenu obj, Integer sortOn) {
        SysMenu menu = new SysMenu();
        menu.setParentId(parentId);
        menu.setMenuId(obj.getMenuId());
        menu.setMenuCode(obj.getMenuCode());
        menu.setName(obj.getName());
        menu.setSortOn(sortOn);
        return menu;
    }

    @Override
    public void existMenuCode(String menuCode, String menuId) {
        LambdaQueryWrapper<SysMenu> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysMenu::getMenuCode, menuCode);
        if (StringUtils.isNotBlank(menuId)) {
            queryWrapper.ne(SysMenu::getMenuId, menuId);
        }
        long count = count(queryWrapper);
        if (count > 0) {
            throw new XServerException("菜单编码已存在");
        }
    }

    @Override
    public void existName(String name, String menuId) {
        LambdaQueryWrapper<SysMenu> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysMenu::getName, name);
        if (StringUtils.isNotBlank(menuId)) {
            queryWrapper.ne(SysMenu::getMenuId, menuId);
        }
        long count = count(queryWrapper);
        if (count > 0) {
            throw new XServerException("路由名已存在");
        }
    }
}
