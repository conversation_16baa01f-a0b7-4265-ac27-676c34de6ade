package com.xong.boot.system.controller;

import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.system.domain.SysArea;
import com.xong.boot.system.service.SysAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/area")
public class SysAreaController
{
    @Autowired
    private SysAreaService sysAreaService;

    /**
     * 查询区划信息列表
     */
    @GetMapping("/list")
    public Result list(SysArea sysArea)
    {
        List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
        return Result.successData(list);
    }
}
