package com.xong.boot.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xong.boot.framework.domain.items.DeptItem;
import com.xong.boot.framework.domain.items.PositionItem;
import com.xong.boot.framework.domain.items.RoleItem;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.system.domain.SysDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 选择器
 * <AUTHOR>
 */
@Mapper
public interface SelectorMapper {

    /**
     * 分页查询部门列表
     * @param ew 条件构建器
     */
    List<DeptItem> listDept(@Param(Constants.WRAPPER) Wrapper<SysDept> ew);

    /**
     * 分页获取部门下的用户
     * @param page      分页
     * @param userScope 范围 0所有用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId    部门ID
     * @param ew        条件构建器
     */
    Page<UserItem> pageUser(Page<UserItem> page, @Param("userScope") Integer userScope, @Param("deptId") String deptId, @Param(Constants.WRAPPER) Wrapper<?> ew);

    /**
     * 分页查询角色列表
     * @param ew 条件构建器
     */
    List<RoleItem> listRole(@Param(Constants.WRAPPER) Wrapper<?> ew);

    /**
     * 分页查询职务列表
     * @param ew 条件构建器
     */
    List<PositionItem> listPosition(@Param(Constants.WRAPPER) Wrapper<?> ew);
}
