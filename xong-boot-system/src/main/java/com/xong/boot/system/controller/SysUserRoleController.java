package com.xong.boot.system.controller;

import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.system.domain.SysUserRole;
import com.xong.boot.system.service.SysUserRoleService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 用户角色管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/userrole")
public class SysUserRoleController extends BaseController<SysUserRoleService, SysUserRole> {
    /**
     * 新增用户角色关系
     * @param params 用户角色集合
     */
    @PostMapping
    @XLog(title = "新增用户角色关系", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:user:role:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody List<SysUserRole> params) {
        if (baseService.saveUserRole(params)) {
            return Result.success("新增成功");
        }
        return Result.fail("新增失败");
    }

    /**
     * 删除用户角色关系
     * @param ids 用户角色ID
     */
    @DeleteMapping
    @XLog(title = "删除用户角色关系", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:user:role:delete')")
    public Result delete(@NotEmpty(message = "参数错误") String[] ids) {
        baseService.removeByIds(Arrays.asList(ids));
        return Result.success("删除成功");
    }

    /**
     * 修改用户角色
     * @param userRole 用户角色
     */
    @PutMapping
    @XLog(title = "修改用户角色", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:user:role:edit')")
    public Result editDept(@NotNull(message = "参数错误") @RequestBody SysUserRole userRole) {
        if (baseService.updateById(userRole)) {
            return Result.success("修改用户角色成功");
        }
        return Result.success("修改用户角色失败");
    }
}
