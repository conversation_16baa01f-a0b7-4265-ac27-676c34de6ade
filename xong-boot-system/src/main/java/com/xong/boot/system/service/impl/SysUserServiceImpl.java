package com.xong.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.xong.boot.common.constant.CacheConstants;
import com.xong.boot.common.crypto.password.PasswordEncoder;
import com.xong.boot.common.domain.User;
import com.xong.boot.common.exception.XFileException;
import com.xong.boot.common.exception.XServerException;
import com.xong.boot.common.lang.UserAvatar;
import com.xong.boot.common.service.impl.BaseServiceImpl;
import com.xong.boot.common.utils.*;
import com.xong.boot.framework.domain.UserDetailsImpl;
import com.xong.boot.framework.domain.items.DeptItem;
import com.xong.boot.framework.domain.items.UserItem;
import com.xong.boot.framework.model.GlobalConfig;
import com.xong.boot.framework.service.GlobalConfigService;
import com.xong.boot.framework.service.TempFileService;
import com.xong.boot.framework.service.UserDetailsService;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.domain.SysUserDept;
import com.xong.boot.system.domain.SysUserPosition;
import com.xong.boot.system.mapper.SysUserDeptMapper;
import com.xong.boot.system.mapper.SysUserMapper;
import com.xong.boot.system.mapper.SysUserPositionMapper;
import com.xong.boot.system.model.resp.UserRoleResp;
import com.xong.boot.system.service.SysUserService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户基础信息 service
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    private final GlobalConfigService globalConfigService;
    private final RedisUtils redisUtils;
    private final PasswordEncoder passwordEncoder;
    private final TempFileService tempFileService;
    //    private final MSConfigService msConfigService;
    private final UserDetailsService userDetailsService;
    private final SysUserDeptMapper sysUserDeptMapper;
    private final SysUserPositionMapper userPositionMapper;

    public SysUserServiceImpl(GlobalConfigService globalConfigService,
                              RedisUtils redisUtils,
                              PasswordEncoder passwordEncoder,
                              TempFileService tempFileService,
                              UserDetailsService userDetailsService,
                              SysUserDeptMapper sysUserDeptMapper,
                              SysUserPositionMapper userPositionMapper) {
        this.globalConfigService = globalConfigService;
        this.redisUtils = redisUtils;
        this.passwordEncoder = passwordEncoder;
        this.tempFileService = tempFileService;
        this.userDetailsService = userDetailsService;
        this.sysUserDeptMapper = sysUserDeptMapper;
        this.userPositionMapper = userPositionMapper;
    }

    private GlobalConfig getGlobalConfig() {
        return globalConfigService.getGlobalConfig();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveUser(SysUser user) {
        existUsername(user.getUsername());
        existUserCode(user.getUserCode(), null);
        existPhone(user.getPhone(), null);
        existIdCard(user.getIdCard(), null);
        existEmail(user.getEmail(), null);
        GlobalConfig globalConfig = getGlobalConfig();
        // 添加默认密码
        user.setSalt(SecureUtils.generateSalt());
        String defaultPassword = parseDefaultPassword(user.getUsername());
        user.setPassword(passwordEncoder.encode(defaultPassword, user.getSalt()));
        // 设置密码过期时间
        user.setCredentialsExpired(LocalDateTimeUtils.toEpochSecond(LocalDateTime.now().plusSeconds(globalConfig.getPasswordExpireTime())));
        // 添加头像 把临时路径中的头像移到头像资源路径
        UserAvatar userAvatar = user.getAvatar();
        user.setAvatar(null);
        if (!save(user)) {
            throw new XServerException("新增用户失败");
        }
        // 添加所属部门
        List<String> deptIds = user.getDeptIds();
        if (deptIds != null && deptIds.size() > 0) {
            List<SysUserDept> listData = deptIds.stream().map(deptId -> {
                SysUserDept userDept = new SysUserDept();
                userDept.setUserId(user.getUserId());
                userDept.setDeptId(deptId);
                userDept.generateId();
                return userDept;
            }).collect(Collectors.toList());
            if (!SqlHelper.retBool(sysUserDeptMapper.insert(listData))) {
                throw new XServerException("新增用户所属部门失败");
            }
        }
        // 添加职务
        List<String> postIds = user.getPostIds();
        if (postIds != null && postIds.size() > 0) {
            List<SysUserPosition> userPositions = postIds.stream().map(postId -> {
                SysUserPosition userPosition = new SysUserPosition();
                userPosition.setUserId(user.getUserId());
                userPosition.setPostId(postId);
                userPosition.generateId();
                return userPosition;
            }).collect(Collectors.toList());
            if (!SqlHelper.retBool(userPositionMapper.insert(userPositions))) {
                throw new XServerException("新增用户职务失败");
            }
        }
        // 添加头像 把临时路径中的头像移到头像资源路径
        if (userAvatar != null && StringUtils.isNotBlank(userAvatar.getRelativePath())) {
            userAvatar.setUserId(user.getUserId());
            userAvatar = tempFileService.copyAvatarUsable(userAvatar);
            SysUser sysUser = new SysUser();
            sysUser.setUserId(user.getUserId());
            sysUser.setAvatar(userAvatar);
            if (!updateById(sysUser)) {
                throw new XFileException("头像上传失败");
            }
        }
        return defaultPassword;
    }

    /**
     * 更新用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(SysUser user) {
        existUserCode(user.getUserCode(), user.getUserId());
        existPhone(user.getPhone(), user.getUserId());
        existIdCard(user.getIdCard(), user.getUserId());
        existEmail(user.getEmail(), user.getUserId());
        // 修改所属部门
        List<String> deptIds = user.getDeptIds();
        if (deptIds != null) {
            sysUserDeptMapper.delete(Wrappers.lambdaQuery(SysUserDept.class)
                    .eq(SysUserDept::getUserId, user.getUserId()));
            if (deptIds.size() > 0) {
                List<SysUserDept> userDepts = deptIds.stream().map(deptId -> {
                    SysUserDept userDept = new SysUserDept();
                    userDept.setUserId(user.getUserId());
                    userDept.setDeptId(deptId);
                    userDept.generateId();
                    return userDept;
                }).collect(Collectors.toList());
                if (!SqlHelper.retBool(sysUserDeptMapper.insertOrUpdate(userDepts))) {
                    throw new XServerException("修改用户所属部门失败");
                }
            }
        }
        // 修改职务
        List<String> postIds = user.getPostIds();
        if (postIds != null) {
            userPositionMapper.delete(Wrappers.lambdaQuery(SysUserPosition.class)
                    .eq(SysUserPosition::getUserId, user.getUserId()));
            if (postIds.size() > 0) {
                List<SysUserPosition> userPositions = postIds.stream().map(postId -> {
                    SysUserPosition userPosition = new SysUserPosition();
                    userPosition.setUserId(user.getUserId());
                    userPosition.setPostId(postId);
                    userPosition.generateId();
                    return userPosition;
                }).collect(Collectors.toList());
                if (!SqlHelper.retBool(userPositionMapper.insertOrUpdate(userPositions))) {
                    throw new XServerException("用户职务修改失败");
                }
            }
        }
        // 修改头像 把临时路径中的头像移到头像资源路径
        UserAvatar userAvatar = user.getAvatar();
        if (userAvatar != null && StringUtils.isNotBlank(userAvatar.getRelativePath())) {
            userAvatar = tempFileService.copyAvatarUsable(userAvatar);
            user.setAvatar(userAvatar);
        } else {
            user.setAvatar(null);
        }
        if (!updateById(user)) {
            throw new XServerException("用户修改失败");
        }
    }

    @Override
    public UserDetailsImpl getUser(String userId) {
        SysUser user = baseMapper.selectById(userId);
        UserDetailsImpl userDetails = BeanUtils.copyProperties(user, UserDetailsImpl.class);
        userDetailsService.completionUserDetails(userDetails);
        return userDetails;
    }

    @Override
    public Page<User> pageUser(Page<User> page, Integer type, String deptId, Wrapper<User> queryWrapper) {
        return baseMapper.selectPageUser(page, type, deptId, queryWrapper);
    }

    /**
     * 解析默认密码
     * @param username 用户名
     */
    @Override
    public String parseDefaultPassword(String username) {
        GlobalConfig globalConfig = getGlobalConfig();
        return globalConfig.getPasswordDefault().replace("{username}", username);
    }

    /**
     * 账号解锁
     * @param usernames 用户账号集合
     */
    @Override
    public void unlock(List<String> usernames) {
        for (String username : usernames) {
            redisUtils.deleteHash(CacheConstants.LOGIN_ACCOUNT_LOCK_KEY, username);
        }
    }

    /**
     * 重置密码为默认密码
     * @param userId 用户ID
     * @return 默认密码
     */
    @Override
    public String resetPassword(String userId) {
        SysUser user = getById(userId);
        if (user == null) {
            throw new XServerException("用户不存在");
        }
        String defaultPassword = parseDefaultPassword(user.getUsername());
        String rawPassword = passwordEncoder.encode(defaultPassword, user.getSalt());
        LambdaUpdateWrapper<SysUser> updateWrapper = Wrappers.lambdaUpdate(SysUser.class)
                .eq(SysUser::getUserId, userId)
                .set(SysUser::getPassword, rawPassword);
        if (update(updateWrapper)) {
            return defaultPassword;
        }
        return null;
    }

    /**
     * 分页获取已删除用户
     * @param page         分页
     * @param queryWrapper 条件构建器
     */
    @Override
    public Page<UserItem> pageRecycle(Page<UserItem> page, Wrapper<SysUser> queryWrapper) {
        return baseMapper.selectPageRecycle(page, queryWrapper);
    }

    /**
     * 还原删除账号
     * @param userIds 用户ID
     */
    @Override
    public boolean restore(List<String> userIds) {
        return baseMapper.restoreDelFlag(userIds);
    }

    /**
     * 分页根据角色ID获取用户列表
     * @param page         分页
     * @param userId       用户ID（必须）
     * @param type         1包含用户ID的角色 2不包含用户ID的角色
     * @param queryWrapper 条件构建器
     */
    @Override
    public Page<UserRoleResp> pageUserRole(Page<UserRoleResp> page, String userId, Integer type, Wrapper<SysRole> queryWrapper) {
        return baseMapper.selectPageUserRole(page, userId, type, queryWrapper);
    }

    /**
     * 获取用户部门列表
     * @param userId 用户ID
     */
    @Override
    public List<DeptItem> listUserDept(String userId) {
        return baseMapper.selectListUserDept(userId);
    }

    /**
     * 是否存在用户名名
     * @param username 用户名
     */
    private void existUsername(String username) {
        if (baseMapper.selectExistUsername(username)) {
            throw new XServerException("账号已存在");
        }
    }

    /**
     * 是否存在用户编号
     * @param userCode 用户编号
     * @param userId   排除用户ID
     */
    private void existUserCode(String userCode, String userId) {
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        if (baseMapper.selectExistUserCode(userCode, userId)) {
            throw new XServerException("用户编码已存在");
        }
    }

    /**
     * 是否存在手机号
     * @param phone  手机号
     * @param userId 排除用户ID
     */
    private void existPhone(String phone, String userId) {
        if (StringUtils.isBlank(phone)) {
            return;
        }
        if (baseMapper.selectExistPhone(phone, userId)) {
            throw new XServerException("手机号已存在");
        }
    }

    /**
     * 是否存在身份证号
     * @param idCard 身份证号
     * @param userId 排除用户ID
     */
    private void existIdCard(String idCard, String userId) {
        if (StringUtils.isBlank(idCard)) {
            return;
        }
        if (baseMapper.selectExistPhone(idCard, userId)) {
            throw new XServerException("身份证号已存在");
        }
    }

    /**
     * 是否存在邮箱地址
     * @param email  邮箱地址
     * @param userId 排除用户ID
     */
    private void existEmail(String email, String userId) {
        if (StringUtils.isBlank(email)) {
            return;
        }
        if (baseMapper.selectExistEmail(email, userId)) {
            throw new XServerException("邮箱地址已存在");
        }
    }

    @Override
    @Cacheable(cacheNames = CacheConstants.USER_CACHE)
    public List<SysUser> selectAll() {
        return this.list();
    }

}
