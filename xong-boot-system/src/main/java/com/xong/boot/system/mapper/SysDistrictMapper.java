package com.xong.boot.system.mapper;

import com.xong.boot.common.mapper.BaseMapper;
import com.xong.boot.framework.domain.items.DistrictItem;
import com.xong.boot.system.domain.SysDistrict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface SysDistrictMapper extends BaseMapper<SysDistrict> {
    /**
     * 查询地区信息
     * @param districtIds 地区ID集
     */
    List<DistrictItem> selectDistricts(@Param("districtIds") String districtIds);
}
