package com.xong.boot.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysDictItem;
import com.xong.boot.system.service.SysDictItemService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 字典项管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/dict/item")
public class SysDictItemController extends BaseController<SysDictItemService, SysDictItem> {
    /**
     * 新增字典项
     * @param dictItem 字典项参数
     */
    @PostMapping
    @XLog(title = "新增字典项", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:dict:item:add')")
    public Result add(@Validated(AddGroup.class) @RequestBody SysDictItem dictItem) {
        baseService.checkValue(dictItem.getDictId(), dictItem.getValue(), null);
        if (baseService.save(dictItem)) {
            return Result.success("新增字典项成功");
        }
        return Result.fail("新增字典项失败");
    }

    /**
     * 删除字典项
     * @param ids 字典ID
     */
    @DeleteMapping
    @XLog(title = "删除字典项", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:dict:item:delete')")
    public Result delete(@NotEmpty(message = "字典项ID不存在") String[] ids) {
        if (baseService.removeByIds(Arrays.asList(ids))) {
            return Result.success("字典项删除成功");
        }
        return Result.fail("字典项删除失败");
    }

    /**
     * 编辑字典项
     * @param dictItem 字典项参数
     */
    @PutMapping
    @XLog(title = "修改字典项", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:dict:item:edit')")
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SysDictItem dictItem) {
        baseService.checkValue(dictItem.getDictId(), dictItem.getValue(), dictItem.getId());
        if (baseService.updateById(dictItem)) {
            return Result.success("字典项修改成功");
        }
        return Result.fail("字典项修改失败");
    }

    /**
     * 获取字典项详情
     * @param id 字典ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:dict:item:*')")
    public Result detail(@NotBlank(message = "字典项ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 获取字典项列表
     * @param dictItem 查询条件
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:dict:item:list')")
    public Result page(SysDictItem dictItem) {
        LambdaQueryWrapper<SysDictItem> queryWrapper = XQueryWrapper.newInstance(dictItem)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByAsc(SysDictItem::getSortOn);
        queryWrapper.orderByAsc(SysDictItem::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }


    /**
     * 获取字典项列表
     * @param dictId 查询条件
     */
    @GetMapping("/info/{dictId}")
    public Result getItemBydictId(@PathVariable String dictId) {
        LambdaQueryWrapper<SysDictItem> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysDictItem::getDictId,dictId);
        queryWrapper.orderByAsc(SysDictItem::getSortOn);
        return Result.successData(baseService.list(queryWrapper));
    }
}
