package com.xong.boot.system.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.system.domain.SysRoleMenu;
import com.xong.boot.system.service.SysRoleMenuService;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 角色菜单管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/rolemenu")
public class SysRoleMenuController extends BaseController<SysRoleMenuService, SysRoleMenu> {
    /**
     * 编辑角色菜单
     * @param params { roleId:角色ID, menuIds: 菜单ID集 }
     */
    @PutMapping("/role")
    @XLog(title = "编辑角色菜单", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:role:menu:edit')")
    public Result editRoleMenu(@NotEmpty(message = "参数错误") @RequestBody JSONObject params) {
        if (baseService.updateRoleMenuByRoleId(params.getString("roleId"), params.getList("menuIds", String.class))) {
            return Result.success("修改成功");
        }
        return Result.fail("修改失败");
    }

    /**
     * 编辑角色菜单数据规则
     * @param data 角色菜单
     */
    @PutMapping("/rule")
    @XLog(title = "修改角色菜单数据规则", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:role:menu:edit')")
    public Result editRule(@Validated(UpdateGroup.class) @RequestBody SysRoleMenu data) {
        if (StringUtils.isBlank(data.getId())) {
            data.generateId();
        }
        baseService.updateById(data);
        return Result.success("规则修改成功");
    }

    /**
     * 获取角色菜单详情
     * @param data 角色ID与菜单ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:role:menu:*')")
    public Result detail(SysRoleMenu data) {
        if (StringUtils.isBlank(data.getId())) {
            data.generateId();
        }
        SysRoleMenu roleMenu = baseService.getById(data.getId());
        if (roleMenu != null) {
            return Result.success().setData(roleMenu);
        }
        return Result.fail("分配菜单后才可进行数据规则分配");
    }

    /**
     * 获取角色菜单列表
     * @param roleMenu 查询条件
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:role:menu:list')")
    public Result list(SysRoleMenu roleMenu) {
        return Result.successData(baseService.list(Wrappers.query(roleMenu)));
    }
}
