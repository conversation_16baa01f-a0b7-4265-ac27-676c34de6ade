package com.xong.boot.system.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.domain.User;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.system.domain.SysRole;
import com.xong.boot.system.domain.SysUser;
import com.xong.boot.system.service.SysUserService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 用户管理
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping(Constants.API_SYSTEM_ROOT_PATH + "/user")
public class SysUserController extends BaseController<SysUserService, SysUser> {
    /**
     * 新增用户
     * @param user 用户参数
     */
    @PostMapping
    @XLog(title = "新增用户", execType = ExecType.INSERT)
    @PreAuthorize("hasAuthority('system:user:add')")
    public Result add(@RequestBody @Validated(AddGroup.class) SysUser user) {
        String defaultPassword = baseService.saveUser(user);
        return Result.success("新增用户成功,默认密码：" + defaultPassword);
    }

    /**
     * 删除用户
     * @param ids 用户ID
     */
    @DeleteMapping
    @XLog(title = "删除用户", execType = ExecType.DELETE)
    @PreAuthorize("hasAuthority('system:user:delete')")
    public Result delete(@NotEmpty(message = "用户ID不存在") String[] ids) {
        if (baseService.removeByIds(Arrays.asList(ids))) {
            return Result.success("用户删除成功");
        }
        return Result.fail("用户删除失败");
    }

    /**
     * 编辑用户
     * @param user 用户参数
     */
    @PutMapping
    @XLog(title = "修改用户", execType = ExecType.UPDATE)
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result edit(@RequestBody @Validated(UpdateGroup.class) SysUser user) {
        baseService.updateUser(user);
        return Result.success("用户修改成功");
    }

    /**
     * 获取用户详情
     * @param id 用户ID
     */
    @GetMapping
    @PreAuthorize("@ss.hasPermission('system:user:*')")
    public Result detail(@NotBlank(message = "用户ID不存在") String id) {
        return Result.successData(baseService.getUser(id));
    }

    /**
     * 分页获取用户角色列表
     * @param type   类型 0所有部门用户 1已分配部门用户 2未分配部门用户 3本级部门用户 4本级及其下级部门用户 5下级部门用户
     * @param deptId 部门ID
     * @param user   查询条件
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result page(Integer type, String deptId, User user) {
        QueryWrapper<User> queryWrapper = XQueryWrapper.newInstance(user)
                .entity2Condition()
                .startAdvancedQuery()
                .startSort();
        return Result.successData(baseService.pageUser(QueryHelper.getPage(), type, deptId, queryWrapper));
    }

    /**
     * 账号解锁
     * userId 用户ID
     */
    @PutMapping("/unlock")
    @PreAuthorize("hasAuthority('system:user:unlock')")
    public Result unlock(@RequestBody @NotNull(message = "参数错误") JSONObject params) {
        JSONArray usernames = params.getJSONArray("usernames");
        if (usernames == null || usernames.size() == 0) {
            return Result.fail("用户账号不存在");
        }
        baseService.unlock(usernames.toJavaList(String.class));
        return Result.success("解锁成功");
    }

    /**
     * 重置密码
     * userId 用户ID
     */
    @PutMapping("/password/reset")
    @XLog(title = "重置密码", execType = ExecType.UPDATE, saveParam = true)
    @PreAuthorize("hasAuthority('system:user:password:reset')")
    public Result resetPassword(@NotBlank(message = "角色ID不存在") String userId) {
        String defaultPassword = baseService.resetPassword(userId);
        if (StringUtils.isNotBlank(defaultPassword)) {
            return Result.success("密码重置成功，默认密码为：" + defaultPassword);
        }
        return Result.fail("密码重置失败");
    }

    /**
     * 分页获取已删除用户
     * @param params 查询条件
     */
    @GetMapping("/page/recycle")
    @PreAuthorize("hasAuthority('system:user:recycle')")
    public Result pageRecycle(SysUser params) {
        QueryWrapper<SysUser> queryWrapper = XQueryWrapper.newInstance(params)
                .entity2Condition()
                .startSort();
        return Result.successData(baseService.pageRecycle(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 还原已删除用户
     * @param params 查询条件
     */
    @PutMapping("/restore")
    @PreAuthorize("hasAuthority('system:user:recycle')")
    @XLog(title = "还原已删除用户", execType = ExecType.UPDATE)
    public Result restore(@RequestBody @NotNull(message = "参数错误") JSONObject params) {
        JSONArray userIds = params.getJSONArray("userIds");
        if (userIds == null || userIds.size() == 0) {
            return Result.fail("用户ID不存在");
        }
        if (baseService.restore(userIds.toJavaList(String.class))) {
            return Result.success("账号还原成功");
        }
        return Result.success("账号还原失败");
    }

    /**
     * 分页获取用户角色列表
     * @param type   1包含用户ID的角色 2不包含用户ID的角色
     * @param userId 用户ID（必须）
     * @param role   查询参数
     */
    @GetMapping("/page/role")
    @PreAuthorize("hasAuthority('system:user:role:list')")
    public Result pageUserRole(@NotNull(message = "获取类型错误") Integer type, @NotBlank(message = "用户ID不存在") String userId, SysRole role) {
        QueryWrapper<SysRole> queryWrapper = XQueryWrapper.newInstance(role)
                .entity2Condition()
                .startAdvancedQuery()
                .startSort();
        return Result.successData(baseService.pageUserRole(QueryHelper.getPage(), userId, type, queryWrapper));
    }

    /**
     * 获取用户部门列表
     * @param userId 用户ID
     */
    @GetMapping("/list/dept")
    @PreAuthorize("hasAuthority('system:user:role:list')")
    public Result listUserDept(@NotBlank(message = "用户ID不存在") String userId) {
        return Result.successData(baseService.listUserDept(userId));
    }
}
