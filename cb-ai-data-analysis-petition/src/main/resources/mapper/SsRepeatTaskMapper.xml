<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.SsRepeatTaskMapper">

    <resultMap type="com.cb.ai.data.analysis.petition.domain.SsRepeatTask" id="SsRepeatTaskMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="beginDate" column="begin_date" jdbcType="VARCHAR"/>
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="similar" column="similar" jdbcType="BIGINT"/>
        <result property="total" column="total" jdbcType="BIGINT"/>
        <result property="errMsg" column="err_msg" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id, t.title, t.begin_date, t.end_date, t.status, t.similar, t.total, t.err_msg,
            t.del_flag, t.remark, t.create_by, t.create_time, t.update_by, t.update_time
    </sql>

    <select id="pageByWrapper" resultMap="SsRepeatTaskMap">
        select
        <include refid="Base_Column_List"/>
        from ss_repeat_task t
        ${ew.customSqlSegment}

    </select>

</mapper>

