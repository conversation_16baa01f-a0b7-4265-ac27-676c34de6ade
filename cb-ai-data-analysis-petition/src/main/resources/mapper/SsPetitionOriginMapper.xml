<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.SsPetitionOriginMapper">

    <select id="findByIdsAndStatus" resultType="com.cb.ai.data.analysis.petition.domain.entity.SsPetitionOriginEntity">
        SELECT *
        FROM ss_petition_origin
        WHERE id IN
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
        <if test="statusList != null and statusList.size() > 0">
            AND
            status IN
            <foreach collection="statusList" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
    </select>
</mapper>
