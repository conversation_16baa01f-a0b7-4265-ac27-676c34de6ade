<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cb.ai.data.analysis.petition.mapper.SsProblemMarketDetailsMapper">

    <resultMap type="com.cb.ai.data.analysis.petition.domain.entity.SsProblemMarketDetailsEntity" id="ProblemMarketDetailsMap">
        <result property="id"                       column="id"    />
        <result property="problemClassifyId"        column="problem_classify_id"    />
        <result property="problemName"              column="problem_name"    />
        <result property="createBy"                 column="create_by"    />
        <result property="createTime"               column="create_time"    />
        <result property="updateBy"                 column="update_by"    />
        <result property="updateTime"               column="update_time"    />
        <result property="remark"                   column="remark"    />
        <result property="problemClassify"          column="problem_classify"    />
        <result property="problemTitle"             column="problem_title"    />
        <result property="fileName"                 column="file_name"    />


    </resultMap>
    <select id="detailsPage" resultMap="ProblemMarketDetailsMap" parameterType="com.cb.ai.data.analysis.petition.domain.vo.SsProblemMarketVo">
        SELECT
            t1.*,
            t2.problem_classify,
            t2.problem_title,
            t2.file_name
        FROM ss_problem_market_details t1
        join ss_problem_market_classify t2 on t1.problem_classify_id=t2.id
        ${ew.customSqlSegment}
    </select>
</mapper>
