# 一、任务说明
### 我会向你提供一个json数组或一个条独立的json，这些数据属于群众信访相关的。其中包含一些文本字段。每条数据的多个字段你需要进行分析，有可能要结合同一条数据的多个字段来分析。之后从这些字段中提取我要的信息

# 二、输出要求
### 1.一定记住，你最终输出给我的信息将是一个标准的json数组，其次，为了和之前提供给你的数据进行对照，你输出的每条数据需要从元数据中提取一个id字段，并放置于你输出的json中
### 2.最终输出的结果，必须按照以下标准json格式和命名给我返回（必须严格遵守），你可以多花一些时间进行分析和校验，务必保证取值的准确性，以及符合我所规定的取值规则
- 整个json的结构和字段取值说明：
    ```json
    [{
    "origin_id": "元数据id",
    "petition_person": "如果能从数据中提取到一位主体人物的姓名，则填充此姓名，找不到任意姓名则填充空",
    "petition_province": "所属省份（如果线索中涉及到案件/事件等，则尽量提取出来一个案发的主要/核心地点，标准行政区划的名称来返回）",
    "petition_city": "所属城市（如果线索中涉及到案件/事件等，则尽量提取出来一个案发的主要/核心地点，标准行政区划的名称来返回）",
    "petition_district": "所属区县（如果线索中涉及到案件/事件等，则尽量提取出来一个案发的主要/核心地点，标准行政区划的名称来返回）",
    "petition_street": "所属街道（如果线索中涉及到案件/事件等，则尽量提取出来一个案发的主要/核心地点，标准行政区划的名称来返回）",
    "user_residence_province": "举报人居住省份，如果找不到，则填写所属省份市即可（提供的线索中有一个信息为住址信息，如果不为空的情况下，请提取其中的省份信息，以标准行政区划的名称来返回，如果有多个则只返回一个）",
    "user_residence_city": "举报人居住市，如果找不到，则填写所属城市即可（提供的线索中有一个信息为住址信息，如果不为空的情况下，请提取其中的市级信息，以标准行政区划的名称来返回，如果有多个则只返回一个）",
    "user_residence_district": "举报人居住区县，如果找不到，则填写所属所属区县即可（提供的线索中有一个信息为住址信息，如果不为空的情况下，请提取其中的区县信息，以标准行政区划的名称来返回，如果有多个则只返回一个）",
    "user_residence_street": "举报人居住街道 如果找不到，则填写所属所属街道即可（提供的线索中有一个信息为住址信息，如果不为空的情况下，请提取其中的街道信息，以标准行政区划的名称来返回，如果有多个则只返回一个）",
    "petition_date": "文本中如果有时间，则提取时间 格式按照 yyyy-MM-dd HH:mm:ss 来返回",
    "register_date": "文本中如果有时间 则提取时间 格式按照 yyyy-MM-dd HH:mm:ss  来返回（这个字段不能为空，为空则填充当前时间）",
    "petition_belong_org": "信访接收机构或信访去向机构 或者 从文本里面提取一个政府机构的名称",
    "petition_handle_suggestion": "处理建议",
    "petition_purpose_category": "根据信访内容综合判断信访目的所属的一级分类，强调群众的核心诉求，取值规则见详细说明。",
    "petition_purpose": "根据信访内容综合判断信访目的所属的二级分类，是在信访目的的一级分类（petition_purpose_category）基础上进一步细化的二级分类，取值规则见详细说明。",
    "petition_domain_category": "信访问题涉及的一级领域分类，表示该诉求主要关联的行业或社会事务范畴（例如：教育、住建、交通、卫生等），强调‘问题是关于哪个领域’，最多两个汉字。",
    "petition_domain": "信访问题涉及的二级领域分类，进一步细化具体所涉问题方向（例如：入学政策、物业管理、道路施工等），应与 petition_domain_category 保持对应关系，建议控制在4-6个汉字以内。",
    "petition_accused_person": "如果信访件中包含对某某公务员/领导，在贪污腐败或渎职等问题上的检举/控告/投诉，则填写被检举/举报公务员的姓名；没有则空；多个则以 , 分隔返回",
    "petition_relation": "如果信访件中可以提取出一些人物关系（关系节点 > 2），请以 JSON 数组的形式返回。source 代表人物/公司/机构，target 代表和 source 存在直接关系的对象，label 代表关系的描述，例如：[{\"source\": \"a\", \"target\": \"b\", \"label\": \"亲属\"}, {\"source\": \"b\", \"target\": \"c\", \"label\": \"同事\"}]",
    "brief": "摘要"
    }]
    ```
- **字段`"petition_purpose_category"`的取值规则：**
  - 请结合所有字段内容，综合判断其所属的一级分类（petition_purpose_category），禁止从"一级内容分类"这个字段中直接取值返回，只允许从以下集合中选择一个最匹配的类别：
    ["农村农业","自然资源","城乡建设","劳动和社会保障","卫生健康","教育","经济管理","市场监管","交通运输","民政与应急","政法","军队事务","科技与信息产业","生态环境","文体旅游","党务政务","组织人事","纪检监察"]
  - 如果无法准确匹配到上述任何一个类别，请统一归类为 "其他"；
  - 严格禁止使用以上规定范围以外的词汇输出，仅允许选择一个最匹配的分类词汇；
  - 分类时应优先考虑群众诉求的本质属性，例如是农村农业、自然资源还是城乡建设等问题。


- **字段`"petition_purpose"`的取值规则：**
  1. **操作流程：**
     - **确定一级分类** (`petition_purpose_category`)：
     - **匹配二级分类** (`petition_purpose`)：
       - 严格从一级分类对应的二级分类列表中选择；
       - 若投诉内容包含明确关键词（如 "废气排放"），优先匹配列表中含该关键词的选项（如 "环境污染与生态破坏"）；
       - 若无关键词，根据处理建议（如 "查封排污设备"）匹配最相关的分类；
       - 若无明确线索且无法匹配，则使用 `"其他"`（需满足特定条件）； 
  2. **禁止行为：**
     - ❌ 禁止直接从 "二级内容分类" 字段取值（如 "拆迁补偿"、“非法占地”）；
     - ❌ 禁止跨一级分类使用二级分类（如一级分类为 "农村农业"，但选择 "土地征收"），此情况需要重新分析；
     - ❌ 禁止自定义或扩展列表外的词汇；
  3. **必须行为：**
     - ✅ 必须基于投诉内容、处理建议等字段的语义分析，从指定分类中选择；
     - ✅ 必须和一级分类值（`"petition_purpose_category"`）做匹配，从其对应的二级集合下选择分类，坚决不允许跨分类选择；
     - ✅ 若无明确匹配项，仅当存在以下情况允许使用 "其他"，否则需要重新分析。
  4. **允许使用 "其他" 的条件：**
     - 投诉内容与一级分类相关但未涵盖在现有二级分类中；
     - 无法从投诉内容推断出具体方向（如 "反映XX问题" 无细节）；
     - 处理建议模糊（如 "已协调处理" 无具体措施）；
  5. **结构化分类映射表：**
      ```json
      {
        "农村农业": ["村务管理", "土地承包经营", "农村宅基地", "扶贫开发", "农副产品流通", "农资农技", "农垦农场", "畜牧养殖", "动物防疫", "水利水电", "水库移民", "惠农补贴", "其他"],
        "自然资源": ["土地资源管理", "土地征收", "不动产登记", "矿产资源管理", "林业管理", "草原管理", "海洋气象", "野生资源管理", "自然保护地管理", "其他"],
        "城乡建设": ["国有土地上房屋征收与补偿", "集体土地上房屋拆迁与补偿", "城市建设和管理", "城乡规划", "住房保障与房地产", "建筑市场", "工程管理", "村镇建设", "其他"],
        "劳动和社会保障": ["城镇职工社会保险", "城乡居民社会保险", "医疗和生育保险", "社保基金", "工资福利", "就业培训", "劳动保护", "劳动关系", "退休政策及待遇", "其他"],
        "卫生健康": ["公共卫生", "医政药政", "医患纠纷", "人口监测与家庭发展", "中医中药管理", "其他"],
        "教育": ["教育体制", "考试招生", "教育行政管理", "教师队伍和待遇", "失学辍学", "教育收费", "其他"],
        "经济管理": ["宏观调控", "商业贸易", "金融", "财税", "国资监管", "能源管理", "企业破产", "审计监督", "营商环境", "共享经济", "其他"],
        "市场监管": ["食品药品", "市场主体登记注册", "反垄断与公平竞争审查", "市场秩序", "质量管理", "知识产权", "其他"],
        "交通运输": ["建设管理", "客货运输", "港航及水上安全管理", "铁路监管", "民航管理", "邮政管理", "出租车管理", "其他"],
        "民政与应急": ["社会组织", "社会救助", "基层选举和社区建设", "区划地名", "社会事务", "养老服务", "儿童服务", "慈善事业促进和社会工作", "灾害救助", "安全生产", "应急救援", "防灾减灾", "其他"],
        "政法": ["法治建设", "诉讼", "仲裁与调解", "行政复议", "生效法律文书执行", "法律服务", "法律监督", "刑罚执行", "警务督察", "社会治安", "交通管理", "刑案侦查", "户籍证件", "移民和出入境管理", "其他"],
        "军队事务": ["军队建设", "军民融合", "军转干部安置", "退役士兵安置", "优待抚恤", "褒扬纪念", "其他"],
        "科技与信息产业": ["科学技术", "信息化建设", "电信", "网络安全", "其他"],
        "生态环境": ["环境污染与生态破坏", "建设项目审批", "环境管理"],
        "文体旅游": ["文化管理", "旅游管理", "文物管理", "体育", "其他"],
        "党务政务": ["党的建设", "政治体制", "民族宗教", "港澳台侨", "国防外交", "群众团体", "宣传舆论", "政务服务", "其他"],
        "组织人事": ["选拔任用", "招录辞退", "编制职位", "人力资源", "机构改革", "离休", "其他"],
        "纪检监察": ["贪污贿赂", "滥用职权", "失职渎职", "干部作风", "党政处分", "其他"],
        "其他": ["历史遗留问题", "领导事务", "表达情感", "其他"]
      }
      ```
  6. **一级分类与二级分类一致性验证：**
     - 在输出 JSON 前，必须验证 `petition_purpose` 是否属于 `petition_purpose_category` 对应的映射列表；
     - 若不一致（如 `petition_purpose_category="农村农业"` 但 `petition_purpose="土地征收"`），必须重新分析并修正；
     - 如果二级分类值不在集合（如 `petition_purpose="水利设施"`），必须重新分析并修正结果；
     - 务必保证两个分类的准确性，以及映射关系性！

# 三、最终目标
### 1.输出的结果中的每条数据一定要按照上面给的英文字段名来进行构建,如果提取不到指定的信息，则以空字符串的形式返回 比如user_residence_city: ""；
### 2.这些数据非常重要吗，你可以多花一些时间进行思考，也可以重复验证后再返回。一定注意以标准的json数组格式来进行返回；
### 3.不允许返回非json数组中的任何内容。
