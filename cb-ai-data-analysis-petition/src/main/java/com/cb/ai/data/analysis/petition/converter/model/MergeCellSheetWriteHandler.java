package com.cb.ai.data.analysis.petition.converter.model;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.Objects;

public class MergeCellSheetWriteHandler implements SheetWriteHandler {

    private final int mergeColumnIndex;
    private final int startRow;

    public MergeCellSheetWriteHandler(int mergeColumnIndex, int startRow) {
        this.mergeColumnIndex = mergeColumnIndex;
        this.startRow = startRow;
    }

    @Override
    public void beforeSheetCreate(com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder writeWorkbookHolder,
                                  com.alibaba.excel.write.metadata.holder.WriteSheetHolder writeSheetHolder) {
        // 不需要实现
    }

    @Override
    public void afterSheetCreate(com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder writeWorkbookHolder,
                                 com.alibaba.excel.write.metadata.holder.WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum <= startRow) {
            return;
        }

        int mergeStart = startRow;
        String previous = getCellValue(sheet, startRow, mergeColumnIndex);

        for (int i = startRow + 1; i <= lastRowNum; i++) {
            String current = getCellValue(sheet, i, mergeColumnIndex);
            if (!Objects.equals(previous, current)) {
                if (i - 1 > mergeStart) {
                    sheet.addMergedRegion(new CellRangeAddress(mergeStart, i - 1, mergeColumnIndex, mergeColumnIndex));
                }
                mergeStart = i;
                previous = current;
            }
        }

        if (lastRowNum > mergeStart) {
            sheet.addMergedRegion(new CellRangeAddress(mergeStart, lastRowNum, mergeColumnIndex, mergeColumnIndex));
        }
    }

    private String getCellValue(Sheet sheet, int rowIdx, int colIdx) {
        if (sheet.getRow(rowIdx) == null || sheet.getRow(rowIdx).getCell(colIdx) == null) {
            return null;
        }
        return sheet.getRow(rowIdx).getCell(colIdx).getStringCellValue();
    }
}

