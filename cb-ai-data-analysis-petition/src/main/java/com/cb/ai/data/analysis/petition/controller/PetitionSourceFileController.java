package com.cb.ai.data.analysis.petition.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.vo.PetitionSourceFile;
import com.cb.ai.data.analysis.petition.domain.vo.SourceFileVo;
import com.cb.ai.data.analysis.petition.service.PetitionSourceFileService;
import com.xong.boot.common.api.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/***
 * <AUTHOR>
 * 信访源文件
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/file")
public class PetitionSourceFileController {

    @Autowired
    private PetitionSourceFileService petitionSourceFileService;

    @Value("${upload.xfFolderId}")
    private String  folderId;

    /***
     * 源文件分页查询列表
     * @param file
     * @return
     */
    @GetMapping("/page")
    public Result page(PetitionSourceFile file) {
        try{
            file.setFolderId(folderId);
            IPage<SourceFileVo> sourceFilePage=petitionSourceFileService.querySourceFileList(file);
            return Result.success(folderId,sourceFilePage);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("源文件列表查询失败！");
        }
    }

    @PostMapping("/listByIds")
    public Result listByIds(@RequestBody List<String> ids){
        try{
            if(!CollectionUtils.isEmpty(ids)&&ids.size()>0){
                return Result.successData(petitionSourceFileService.listByIds(ids));
            }else{
                return Result.successData(null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取问题信息失败！");
        }
    }
}
