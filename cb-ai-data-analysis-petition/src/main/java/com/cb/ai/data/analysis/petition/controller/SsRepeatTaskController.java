package com.cb.ai.data.analysis.petition.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.domain.SsRepeatResultGroup;
import com.cb.ai.data.analysis.petition.domain.SsRepeatTask;
import com.cb.ai.data.analysis.petition.domain.vo.SsRepeatTaskVo;
import com.cb.ai.data.analysis.petition.service.SsRepeatResultGroupService;
import com.cb.ai.data.analysis.petition.service.SsRepeatResultItemService;
import com.cb.ai.data.analysis.petition.service.SsRepeatTaskService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.utils.StringUtils;
import com.xong.boot.framework.utils.QueryHelper;
import com.xong.boot.framework.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH + "/repeatTask")
public class SsRepeatTaskController extends BaseController<SsRepeatTaskService, SsRepeatTask> {

    @Autowired
    private SsRepeatResultGroupService resultGroupService;
    @Autowired
    private SsRepeatResultItemService repeatResultItemService;

    @PostMapping("/createTask")
    public Result createTask(@RequestBody SsRepeatTaskVo.CreateReq req) {
        if (req.getThreshold() < 0.01 || req.getThreshold() > 1) {
            req.setThreshold(0.8);
        }
        String beginDate = req.getBeginDate();
        String endDate = req.getEndDate();
        if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
            return Result.fail("请选择时间范围");
        }
        baseService.createTask(req);
        return Result.success("创建成功！后台分析中");
    }

    /**
     * 执行任务
     *
     * @param taskId
     * @param threshold
     * @param force
     * @return
     */
    @PostMapping("/startTask")
    public Result startAsyncTask(@RequestParam("taskId") String taskId,
                                 @RequestParam("threshold") double threshold,
                                 @RequestParam("force") boolean force) {
        baseService.startAsyncTask(taskId, threshold, force);
        return Result.success("开始分析");
    }

    /**
     * 获取重复件分析任务列表
     *
     * @param ssRepeatTask
     * @return
     */
    @GetMapping("/pageBySelf")
    public Result pageBySelf(SsRepeatTask ssRepeatTask) {
        String username = SecurityUtils.getUsername();
        ssRepeatTask.setCreateBy(username);
        return Result.successData(baseService.pageBySelf(QueryHelper.getPage(), ssRepeatTask));
    }

    /**
     * 删除任务
     * @param id
     * @return
     */
    @DeleteMapping("/delTask/{id}")
    public Result delTask(@PathVariable("id") String id) {
        baseService.delTask(id);
        return Result.success();
    }

    /**
     * 获取任务结果
     * @param id
     * @param entity
     * @return
     */
    @GetMapping("/getTaskResult/{id}")
    public Result getTaskResult(@PathVariable("id") String id, SsRepeatResultGroup entity) {
        Page<SsRepeatResultGroup> pageData = baseService.getRepeatTaskResult(id, QueryHelper.getPage(), entity);
        return Result.successData(pageData);
    }

}
