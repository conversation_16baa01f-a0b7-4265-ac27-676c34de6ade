package com.cb.ai.data.analysis.petition.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.model.DocOrgName;
import com.cb.ai.data.analysis.petition.converter.model.DocRedhead;
import com.cb.ai.data.analysis.petition.converter.model.DocTitle;
import com.cb.ai.data.analysis.petition.converter.model.DocumentInfo;
import com.cb.ai.data.analysis.petition.converter.pipe.DocumentInfoHelper;
import com.cb.ai.data.analysis.petition.converter.pipe.IPipe;
import com.cb.ai.data.analysis.petition.converter.pipe.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公文 工具类
 * <AUTHOR>
 */
public class DocFormatConverter {

    public static void formatDocument(XWPFDocument xwpfDocument, DocConfig config) {
        DocumentInfo documentInfo = DocumentInfoHelper.getDocumentInfo(xwpfDocument);
        List<IPipe> pipeList = new ArrayList<>();
        pipeList.add(new PartNumberPipe());
        pipeList.add(new SecretPipe());
        pipeList.add(new UrgencyPipe());
        pipeList.add(new IssuedNumberPipe());
        pipeList.add(new RedTitlePipe());
        pipeList.add(new ParagraphTrimPipe());
        pipeList.add(new RunMergePipe());
        pipeList.add(new SymbolPipe());
        pipeList.add(new TitlePipe());
//        pipeList.add(new SubtitlePipe());
        pipeList.add(new MainDeliveryPipe());
        pipeList.add(new ImplementPipe());
        pipeList.add(new DatePipe());
        pipeList.add(new OrgPipe());
        pipeList.add(new AnnotationPipe());
        pipeList.add(new SerialNumber1Pipe());
        pipeList.add(new SerialNumber2Pipe());
        pipeList.add(new SerialNumber3Pipe());
        pipeList.add(new SerialNumber4Pipe());
        pipeList.add(new AttachHeadPipe());
        pipeList.add(new AttachExplainPipe());
        pipeList.add(new AttachExplainNumBeginPipe());
        pipeList.add(new LongLinePipe());
        pipeList.add(new CommonPipe());

        int mainDeliveryIndex = -1; // 主送机构位置
        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
            XWPFParagraph paragraph = paragraphs.get(i);
            for (IPipe pipe : pipeList) {
                if (pipe.execute(xwpfDocument, paragraph, config, i, documentInfo)) {
                    if (mainDeliveryIndex == -1 && pipe instanceof MainDeliveryPipe) {
                        mainDeliveryIndex = i;
                    }
//                    System.out.println(pipe.getClass().getName());
//                    System.out.println(paragraph.getText());
                    break;
                }
            }
        }

        DocRedhead redhead = documentInfo.getRedhead();
        // 没套红之前处理
        if (redhead == null) {
            //设置title，上有红头时为红头分割线下空2行的位置，下有有主送单位时与主送单位之间空1行
            insertParagraphAroundMainTitle(xwpfDocument, documentInfo);
            //附件之前插入空行（新段落）
            insertParagraphBeforeAttach(xwpfDocument);

            //删除附件头下存在的多余空行 AttachHeadPipe
            delAttachHeadBlankLines(xwpfDocument);

            //删除附注之前空行
            delAnnotationBeforeBlankLines(xwpfDocument);

            // 格式化发文机关署名前的空格
            formatOrgNameBeforeBlankLine(xwpfDocument);

            if (mainDeliveryIndex > 0) { // 有主送机构在主送机构前加空白行
                if (mainDeliveryIndex < xwpfDocument.getParagraphs().size()) {
                    XWPFParagraph paragraph = xwpfDocument.getParagraphs().get(mainDeliveryIndex - 1);
                    if (paragraph != null && StringUtils.isNotBlank(paragraph.getText())) {
                        xwpfDocument.insertNewParagraph(xwpfDocument.getParagraphs().get(mainDeliveryIndex).getCTP().newCursor());
                    }
                }
            } else { // 没有主送机构在标题后空白行
                if (documentInfo.getTitles() != null && documentInfo.getTitles().size() > 0 && documentInfo.getTitles().get(documentInfo.getTitles().size() - 1).getPos() < xwpfDocument.getParagraphs().size() - 1) {
                    XWPFParagraph titleParagraph = xwpfDocument.getParagraphs().get(documentInfo.getTitles().get(documentInfo.getTitles().size() - 1).getPos() + 1);
                    if (titleParagraph != null && StringUtils.isNotBlank(titleParagraph.getText())) {
                        xwpfDocument.insertNewParagraph(titleParagraph.getCTP().newCursor());
                    }
                }
            }
        }

        if (config.isFormatTableWidth()) {
            beautifyTableAuto(xwpfDocument); // 排版表格宽度自适应
        }
    }

    /**
     * 设置title，上有红头时为红头分割线下空2行的位置，下有有主送单位时与主送单位之间空1行
     * @param xwpfDocument
     * @param documentInfo
     */
    public static void insertParagraphAroundMainTitle(XWPFDocument xwpfDocument, DocumentInfo documentInfo) {
        if (CollectionUtil.isNotEmpty(documentInfo.getTitles())) {
            DocTitle docTitle = documentInfo.getTitles().get(0);
            Integer pos = docTitle.getPos();
            //有红头时
            if (null != documentInfo.getRedhead()) {
                XWPFParagraph titleParagraph = xwpfDocument.getParagraphs().get(pos);
                if (pos == 1) {
                    xwpfDocument.insertNewParagraph(titleParagraph.getCTP().newCursor());
                    docTitle.setPos(pos + 1);
                } else if (pos == 0) {
                    xwpfDocument.insertNewParagraph(titleParagraph.getCTP().newCursor());
                    xwpfDocument.insertNewParagraph(titleParagraph.getCTP().newCursor());
                    docTitle.setPos(pos + 2);
                }
            }

            List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
            int mainDeliveryPos = -1;
            for (int i = 0; i < paragraphs.size(); i++) {
                XWPFParagraph paragraph = paragraphs.get(i);
                String text = paragraph.getText().trim();
                if (StringUtils.isBlank(text)) {
                    continue;
                }
                if (i < 20 && ReUtil.contains("[省市区县镇村组府局厅部委校位体构院业门司长理总站位][:：]$", text)) {
                    mainDeliveryPos = i;
                    break;
                }
            }
            //有主送单位
            if (mainDeliveryPos > -1) {
                if (mainDeliveryPos - docTitle.getPos() < 2) {
                    xwpfDocument.insertNewParagraph(paragraphs.get(mainDeliveryPos).getCTP().newCursor());
                }
            }
        }
    }

    /**
     * 附件之前插入空行（新段落）
     * @param xwpfDocument 文档
     */
    public static void insertParagraphBeforeAttach(XWPFDocument xwpfDocument) {
        List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
            XWPFParagraph paragraph = paragraphs.get(i);
            String text = paragraph.getText().trim();
            boolean currIsAttachFirst = ReUtil.isMatch("^[  　]*附[  　]{0,2}件[:：].+", text);
            int pre = i - 1;
            if (pre >= 0) {
                XWPFParagraph preParagraph = paragraphs.get(pre);
                boolean preIsEmpty = StringUtils.isBlank(preParagraph.getText().trim());
                // 没有空白行则新增
                if (currIsAttachFirst && !preIsEmpty) {
                    CTP targetCTP = paragraph.getCTP();
                    xwpfDocument.insertNewParagraph(targetCTP.newCursor());
                }
            }

        }
    }

    /**
     * 排版表格宽度
     * @param xwpfDocument Doc
     */
    public static void beautifyTableAuto(XWPFDocument xwpfDocument) {
        List<XWPFTable> tableList = xwpfDocument.getTables();
        if (tableList == null || tableList.size() == 0) {
            return;
        }
        for (XWPFTable table : tableList) {
            CTTbl ctTbl = table.getCTTbl();
            CTTblPr ctTblPr = ctTbl.getTblPr();
            ctTblPr.unsetTblStyle();
            List<CTRow> ctRowList = ctTbl.getTrList();
            for (CTRow ctRow : ctRowList) {
                List<CTTc> ctTcList = ctRow.getTcList();
                for (CTTc ctTc : ctTcList) {
                    CTTcPr ctTcPr = ctTc.getTcPr();
                    if (ctTcPr.isSetTcMar()) {
                        ctTcPr.unsetTcMar();
                    }
                }
            }
            table.setWidth(5000);
            table.setWidthType(TableWidthType.PCT);
            table.setTableAlignment(TableRowAlign.CENTER);
        }
    }


    /**
     * 删除附件头下存在的多余空行
     * @param xwpfDocument
     */
    public static void delAttachHeadBlankLines(XWPFDocument xwpfDocument) {
        List<IBodyElement> elements = xwpfDocument.getBodyElements();
        List<Integer> dels = new LinkedList<>();
        for (int i = elements.size() - 1; i >= 0; i--) {
            IBodyElement element = elements.get(i);
            if (element.getElementType() != BodyElementType.PARAGRAPH) {
                continue;
            }
            XWPFParagraph paragraph = (XWPFParagraph) element;
            String text = paragraph.getText().trim();
            boolean currIsAttachHead = ReUtil.isMatch("^附[  　]*件[  　]*[\\d]?", text);
            if (currIsAttachHead) {
                int next = i + 1;
                int k = next;
                for (; k < elements.size(); k++) {
                    IBodyElement e = elements.get(k);
                    XWPFParagraph p = (XWPFParagraph) e;
                    if (e.getElementType() != BodyElementType.PARAGRAPH) {
                        break;
                    }
                    if (StringUtils.isNotBlank(p.getText().trim())) {
                        break;
                    } else {
                        dels.add(k);
                    }
                }
            }
        }
        dels = dels.stream().distinct().sorted().collect(Collectors.toList());
        for (int i = 0; i < dels.size(); i++) {
            xwpfDocument.removeBodyElement(dels.get(i) - i);
        }
    }

    /**
     * 删除附注之前空白行
     * @param xwpfDocument
     */
    public static void delAnnotationBeforeBlankLines(XWPFDocument xwpfDocument) {
        List<IBodyElement> elements = xwpfDocument.getBodyElements();
        List<Integer> dels = new LinkedList<>();
        for (int i = elements.size() - 1; i >= 0; i--) {
            IBodyElement element = elements.get(i);
            if (element.getElementType() != BodyElementType.PARAGRAPH) {
                continue;
            }
            XWPFParagraph paragraph = (XWPFParagraph) element;
            String text = paragraph.getText().trim();
            boolean currIsAnnotation = ReUtil.isMatch("^[　]*[(（].+[）)][　]*$", text);
            if (currIsAnnotation) {
                int next = i - 1;
                int k = next;
                for (; k < elements.size(); k--) {
                    IBodyElement e = elements.get(k);
                    if (e.getElementType() != BodyElementType.PARAGRAPH) {
                        break;
                    }
                    XWPFParagraph p = (XWPFParagraph) e;
                    if (StringUtils.isNotBlank(p.getText().trim())) {
                        break;
                    } else {
                        dels.add(k);
                    }
                }
            }
        }
        dels = dels.stream().distinct().sorted().collect(Collectors.toList());
        for (int i = 0; i < dels.size(); i++) {
            xwpfDocument.removeBodyElement(dels.get(i) - i);
        }
    }

    /**
     * 格式化发文机关署名前空白行，若存在多行，只保留一行，没有则新增一行
     * @param xwpfDocument
     */
    public static void formatOrgNameBeforeBlankLine(XWPFDocument xwpfDocument) {
        DocumentInfo documentInfo = DocumentInfoHelper.getDocumentInfo(xwpfDocument);
        DocOrgName docOrgName = documentInfo.getOrgName();
        if (docOrgName != null) {
            List<Integer> dels = new LinkedList<>();
            // 1.先查发文机关署名前面有没有空行，有则先全部删除
            List<IBodyElement> elements = xwpfDocument.getBodyElements();
            for (int i = elements.size() - 1; i >= 0; i--) {
                IBodyElement element = elements.get(i);
                System.out.println(i + "=" + element.getElementType());
                if (element.getElementType() != BodyElementType.PARAGRAPH) {
                    continue;
                }
                if (element == docOrgName.getSection()) {
                    int next = i - 1;
                    int k = next;
                    for (; k < elements.size(); k--) {
                        IBodyElement e = elements.get(k);
                        if (e.getElementType() != BodyElementType.PARAGRAPH) {
                            break;
                        }
                        XWPFParagraph p = (XWPFParagraph) e;
                        if (StringUtils.isNotBlank(p.getText().trim())) {
                            break;
                        } else {
                            dels.add(k);
                        }
                    }
                }
            }
            dels = dels.stream().distinct().sorted().collect(Collectors.toList());
            for (int i = 0; i < dels.size(); i++) {
                xwpfDocument.removeBodyElement(dels.get(i) - i);
            }
            // 2.在发文机关署名前面新增空白行
            List<XWPFParagraph> finalParagraphs = xwpfDocument.getParagraphs();
            DocumentInfo finalDocumentInfo = DocumentInfoHelper.getDocumentInfo(xwpfDocument);
            XWPFParagraph paragraph = finalParagraphs.get(finalDocumentInfo.getOrgName().getStart());
            CTP targetCTP = paragraph.getCTP();
            xwpfDocument.insertNewParagraph(targetCTP.newCursor());
        }
    }
}
