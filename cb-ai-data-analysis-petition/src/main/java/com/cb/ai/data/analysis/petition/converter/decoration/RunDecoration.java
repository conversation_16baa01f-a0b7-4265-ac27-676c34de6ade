package com.cb.ai.data.analysis.petition.converter.decoration;

import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;

/**
 * 字体格式操作
 * <AUTHOR>
 */
public class RunDecoration extends BaseDecoration {
    private final XWPFRun run;
    private final CTR ctr;
    private CTRPr ctrPr;

    public RunDecoration(XWPFRun run) {
        this.run = run;
        this.ctr = run.getCTR();
        this.ctrPr = ctr.getRPr();
        if (ctrPr == null) {
            ctrPr = ctr.addNewRPr();
        }
    }

    public static RunDecoration newInstance(XWPFRun run) {
        return new RunDecoration(run);
    }

    public XWPFRun getRun() {
        return run;
    }

    public CTR getCtr() {
        return ctr;
    }

    public CTRPr getCtrPr() {
        return ctrPr;
    }

    /**
     * 初始化字体格式
     */
    public RunDecoration init() {
        if (ctrPr.sizeOfRStyleArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfSzArray(); i++) {
                ctrPr.removeRStyle(i);
            }
        }
        if (ctrPr.sizeOfBArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfBArray(); i++) {
                ctrPr.removeB(0);
            }
        }
        if (ctrPr.sizeOfBCsArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfBCsArray(); i++) {
                ctrPr.removeBCs(0);
            }
        }
        if (ctrPr.sizeOfIArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfIArray(); i++) {
                ctrPr.removeI(0);
            }
        }
        if (ctrPr.sizeOfICsArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfICsArray(); i++) {
                ctrPr.removeICs(0);
            }
        }
        if (ctrPr.sizeOfCapsArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfCapsArray(); i++) {
                ctrPr.removeCaps(0);
            }
        }
        if (ctrPr.sizeOfSmallCapsArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfSmallCapsArray(); i++) {
                ctrPr.removeSmallCaps(0);
            }
        }
        if (ctrPr.sizeOfStrikeArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfStrikeArray(); i++) {
                ctrPr.removeStrike(0);
            }
        }
        if (ctrPr.sizeOfDstrikeArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfDstrikeArray(); i++) {
                ctrPr.removeDstrike(0);
            }
        }
        if (ctrPr.sizeOfVanishArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfVanishArray(); i++) {
                ctrPr.removeVanish(0);
            }
        }
        if (ctrPr.sizeOfColorArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfColorArray(); i++) {
                ctrPr.removeColor(0);
            }
        }
        if (ctrPr.sizeOfSpacingArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfSpacingArray(); i++) {
                ctrPr.removeSpacing(0);
            }
        }
        if (ctrPr.sizeOfHighlightArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfHighlightArray(); i++) {
                ctrPr.removeHighlight(0);
            }
        }
        if (ctrPr.sizeOfUArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfUArray(); i++) {
                ctrPr.removeU(0);
            }
        }
        if (ctrPr.sizeOfVertAlignArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfVertAlignArray(); i++) {
                ctrPr.removeVertAlign(0);
            }
        }
        if (ctrPr.sizeOfRFontsArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfRFontsArray(); i++) {
                ctrPr.removeRFonts(i);
            }
        }
        ctrPr.addNewRFonts();
        if (ctrPr.sizeOfSzArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfSzArray(); i++) {
                ctrPr.removeSz(i);
            }
        }
        ctrPr.addNewSz();
        if (ctrPr.sizeOfSzCsArray() > 0) {
            for (int i = 0; i < ctrPr.sizeOfSzCsArray(); i++) {
                ctrPr.removeSzCs(i);
            }
        }
        ctrPr.addNewSzCs();
        return this;
    }

    /**
     * 设置字体
     * @param eastAsia 中文字体
     * @param ascii    英文字体
     */
    public RunDecoration setFonts(String eastAsia, String ascii) {
        if (ctrPr.sizeOfRFontsArray() == 0) {
            ctrPr.addNewRFonts();
        }
        ctrPr.setRFontsArray(0, getCTFonts(eastAsia, ascii));
        return this;
    }

    /**
     * 设置字体大小
     * @param val 中文大小
     */
    public RunDecoration setSize(Object val) {
        if (ctrPr.sizeOfSzArray() == 0) {
            ctrPr.addNewSz();
        }
        ctrPr.setSzArray(0, getCTHpsMeasure(val));
        if (ctrPr.sizeOfSzCsArray() == 0) {
            ctrPr.addNewSzCs();
        }
        ctrPr.setSzCsArray(0, getCTHpsMeasure(val));
        return this;
    }
}
