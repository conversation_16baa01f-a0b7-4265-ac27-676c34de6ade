package com.cb.ai.data.analysis.petition.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cb.ai.data.analysis.petition.constant.Constants;
import com.cb.ai.data.analysis.petition.domain.entity.SsConversationRecordEntity;
import com.cb.ai.data.analysis.petition.domain.vo.SsConversationRecordVo;
import com.cb.ai.data.analysis.petition.service.SsConversationRecordService;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.exception.XServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/***
 * <AUTHOR>
 * 谈话解析
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH+"/talk")
public class SsConversationRecordController {

    @Autowired
    private SsConversationRecordService ssConversationRecordService;


    /***
     * 分页查询谈话列表
     * @param ssConversationRecord
     * @return
     */
    @GetMapping("/page")
    public Result page(SsConversationRecordVo ssConversationRecord) {
        try{
            Page<SsConversationRecordEntity> knowledgeBasePage=ssConversationRecordService.selectByPage(ssConversationRecord);
            return Result.successData(knowledgeBasePage);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("查询谈话列表失败！");
        }
    }

    /***
     * 图片批量上传
     * @param
     * @return
     */
    @PostMapping("/batchImagesUpload")
    public Result batchImagesUpload(@RequestParam("files") List<MultipartFile> files) {
        try{
            ssConversationRecordService.ocrBatchAnalysis(files);
            return Result.successData("成功");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("批量上传失败！");
        }
    }
    /***
     * 图片上传
     * @param
     * @return
     */
    @PostMapping("/ocrAnalysis")
    public Result ocrAnalysis(@RequestParam("file") MultipartFile file) {
        try{
            return ssConversationRecordService.ocrAnalysis(file);
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("OCR解析失败！");
        }
    }

    /***
     * 保存解析内容
     * @param
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public Result saveOrUpdate(@RequestBody SsConversationRecordVo vo) {
        try{
            int i=ssConversationRecordService.saveOrUpdate(vo);
            return Result.successData("提交保存任务成功");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("保存解析内容失败！");
        }
    }

    /**
     * 根据ID删除解析内容
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable String id){
        try{
            ssConversationRecordService.removeById(id);
            return Result.success("删除解析内容成功！");
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("删除解析内容失败！");
        }
    }
    @PostMapping("/listByIds")
    public Result listByIds(@RequestBody List<String> ids){
        try{
            if(!CollectionUtils.isEmpty(ids)&&ids.size()>0){
                return Result.successData(ssConversationRecordService.listByIds(ids));
            }else{
                return Result.successData(null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return Result.fail("获取谈话解析失败！");
        }
    }
}
