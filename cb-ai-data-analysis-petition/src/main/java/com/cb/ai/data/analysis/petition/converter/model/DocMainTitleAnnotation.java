package com.cb.ai.data.analysis.petition.converter.model;//package com.cb.wps.docassist.converter.quickformat.model;
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 公文主标题下附注
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class DocMainTitleAnnotation extends BaseDoc {
//    /**
//     * 附注组
//     */
//    private List<String> texts = new ArrayList<>();
//
//    public int size() {
//        return texts.size();
//    }
//}
