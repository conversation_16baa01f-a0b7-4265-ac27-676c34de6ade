package com.cb.ai.data.analysis.petition.converter.model;//package com.cb.wps.docassist.converter.quickformat.model;
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 公文附件说明
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class DocAttachExplain extends BaseDoc {
//    /**
//     * 附件列表
//     */
//    private List<String> texts = new ArrayList<>();
//
//    /**
//     * 附件数
//     */
//    public int size() {
//        return texts.size();
//    }
//}
