package com.cb.ai.data.analysis.petition.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cb.ai.data.analysis.file.domain.SuperviseResourceFile;
import com.cb.ai.data.analysis.petition.domain.entity.SsWordExcelGenerateLogEntity;
import com.cb.ai.data.analysis.petition.service.SsWordExcelGenerateLogService;
import com.xong.boot.common.annotation.XLog;
import com.xong.boot.common.api.Result;
import com.xong.boot.common.constant.Constants;
import com.xong.boot.common.controller.BaseController;
import com.xong.boot.common.enums.ExecType;
import com.xong.boot.common.valid.AddGroup;
import com.xong.boot.common.valid.UpdateGroup;
import com.xong.boot.framework.query.XQueryWrapper;
import com.xong.boot.framework.utils.QueryHelper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * (SsWordExcelGenerateLog)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-21 16:11:34
 */
@RestController
@RequestMapping(Constants.API_PETITION_ROOT_PATH + "/word/converter")
public class SsWordExcelGenerateLogController extends BaseController<SsWordExcelGenerateLogService, SsWordExcelGenerateLogEntity> {

    /**
     * 分页获取
     *
     * @param ssWordExcelGenerateLog 查询实体
     * @return 相关数据
     */
    @GetMapping("/page")
    public Result page(SsWordExcelGenerateLogEntity ssWordExcelGenerateLog) {
        LambdaQueryWrapper<SsWordExcelGenerateLogEntity> queryWrapper = XQueryWrapper.newInstance(ssWordExcelGenerateLog)
                .startAdvancedQuery()
                .startSort()
                .lambda();
        queryWrapper.orderByDesc(SsWordExcelGenerateLogEntity::getCreateTime);
        return Result.successData(baseService.page(QueryHelper.getPage(), queryWrapper));
    }

    /**
     * 批量word转换
     *
     * @param fileList 文件实体数组对象
     * @return 新增结果
     */
    @PostMapping("/wordToExcel")
    @XLog(title = "批量word转换", execType = ExecType.INSERT)
    public Result wordToExcel(@Validated(AddGroup.class) @RequestBody List<SuperviseResourceFile> fileList) {
        try {
            baseService.wordToExcel(fileList);
            return Result.success("任务提交成功，请在下方表格中查看生成状态及下载");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("任务提交失败：" + e.getMessage());
        }
    }

    /**
     * 获取
     *
     * @param id 主键
     * @return 数据详情
     */
    @GetMapping
    public Result detail(@NotBlank(message = "ID不存在") String id) {
        return Result.successData(baseService.getById(id));
    }

    /**
     * 新增
     *
     * @param ssWordExcelGenerateLog 实体对象
     * @return 新增结果
     */
    @PostMapping
    @XLog(title = "新增", execType = ExecType.INSERT)
    public Result add(@Validated(AddGroup.class) @RequestBody SsWordExcelGenerateLogEntity ssWordExcelGenerateLog) {
        if (baseService.save(ssWordExcelGenerateLog)) {
            return Result.success("新增成功！");
        }
        return Result.fail("新增失败！");
    }

    /**
     * 修改
     *
     * @param ssWordExcelGenerateLog 实体对象
     * @return 修改结果
     */
    @PutMapping
    @XLog(title = "修改", execType = ExecType.UPDATE)
    public Result edit(@Validated(UpdateGroup.class) @RequestBody SsWordExcelGenerateLogEntity ssWordExcelGenerateLog) {
        if (baseService.updateById(ssWordExcelGenerateLog)) {
            return Result.success("修改成功！");
        }
        return Result.fail("修改失败！");
    }

    /**
     * 删除
     *
     * @param ids 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @XLog(title = "删除", execType = ExecType.DELETE)
    public Result delete(@NotEmpty(message = "ID不存在") String[] ids) {
        List<String> list = Arrays.asList(ids);
        if (baseService.removeByIds(list)) {
            return Result.success("删除成功！");
        }
        return Result.fail("删除失败！");
    }
}

