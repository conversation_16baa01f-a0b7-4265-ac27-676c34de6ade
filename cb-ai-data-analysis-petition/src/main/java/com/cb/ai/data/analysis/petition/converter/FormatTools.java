package com.cb.ai.data.analysis.petition.converter;

import cn.hutool.core.util.ReUtil;
import com.cb.ai.data.analysis.petition.converter.decoration.ParagraphDecoration;
import com.cb.ai.data.analysis.petition.converter.decoration.RunDecoration;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSpacing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;

import java.util.ArrayList;
import java.util.List;

/**
 * 排版工具
 * <AUTHOR>
 */
public class FormatTools {

    /**
     * 获取表格中所有段落
     */
    public static List<XWPFParagraph> getParagraphs(XWPFTable table) {
        List<XWPFParagraph> paragraphs = null;
        List<XWPFTableRow> rows = table.getRows();
        for (XWPFTableRow row : rows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                List<XWPFParagraph> cellParagraphs = cell.getParagraphs();
                if (cellParagraphs != null && cellParagraphs.size() > 0) {
                    if (paragraphs == null) {
                        paragraphs = new ArrayList<>();
                    }
                    paragraphs.addAll(cellParagraphs);
                }
            }
        }
        return paragraphs;
    }

    /**
     * 获取表格种所有XWPFRun
     */
    public static List<XWPFRun> getRun(XWPFTable table) {
        List<XWPFRun> runs = null;
        List<XWPFTableRow> rows = table.getRows();
        for (XWPFTableRow row : rows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                List<XWPFParagraph> cellParagraphs = cell.getParagraphs();
                for (XWPFParagraph cellParagraph : cellParagraphs) {
                    List<XWPFRun> cellRuns = cellParagraph.getRuns();
                    if (cellRuns != null && cellRuns.size() > 0) {
                        if (runs == null) {
                            runs = new ArrayList<>();
                        }
                        runs.addAll(cellRuns);
                    }
                }
            }
        }
        return runs;
    }

    /**
     * 格式化普通段落
     * @param paragraph 段落
     * @param config    配置
     */
    public static ParagraphDecoration formatParagraph(XWPFParagraph paragraph, DocConfig config) {
        return ParagraphDecoration.newInstance(paragraph)
                .init()
                .setCTJcBoth()
                .setWidowControl(config.isWidowControl())
                .setCTSpacing(config.getLineSpacingRule(), config.getLineSpacing());
    }

    /**
     * 格式化标题段落
     * @param paragraph 段落
     * @param config    配置
     */
    public static void formatParagraphTitle(XWPFParagraph paragraph, DocConfig config) {
        ParagraphDecoration.newInstance(paragraph)
                .init()
                .setCTJcCenter()
                .setWidowControl(config.isWidowControl())
                .setCTSpacing(config.getTitleLineSpacingRule(), config.getTitleLineSpacing());
    }

    /**
     * 格式化普通段落缩进
     * @param paragraph 段落
     * @param config    配置
     */
    public static void formatParagraphInd(XWPFParagraph paragraph, DocConfig config) {
        formatParagraph(paragraph, config).setFirstLineChars(200L);
    }

    /**
     * 格式化普通段落右缩进
     * @param paragraph 段落
     * @param config    配置
     */
    public static void formatParagraphRightInd(XWPFParagraph paragraph, DocConfig config) {
        formatParagraphRightInd(paragraph, config, 200L);
    }

    /**
     * 格式化普通段落右缩进
     * @param paragraph 段落
     * @param config    配置
     * @param c         缩进值
     */
    public static void formatParagraphRightInd(XWPFParagraph paragraph, DocConfig config, Long c) {
        formatParagraph(paragraph, config).setCTJcRight().setRightChars(c);
    }

    /**
     * 格式化普通段落左缩进
     * @param paragraph
     * @param config
     * @param c
     */
    public static void formatParagraphLeftInd(XWPFParagraph paragraph, DocConfig config, Long c) {
        formatParagraph(paragraph, config).setCTJcLeft().setLeftChars(c);
    }

    /**
     * 设置普通段落居中对齐
     * @param paragraph
     */
    public static void formatParagraphCenter(XWPFParagraph paragraph,DocConfig config) {
        ParagraphDecoration.newInstance(paragraph)
                .init()
                .setCTJcCenter()
                .setWidowControl(config.isWidowControl());
    }

    /**
     * 设置段落左对齐
     * @param paragraph
     */
    public static void formatParagraphLeft(XWPFParagraph paragraph,DocConfig config) {
        ParagraphDecoration.newInstance(paragraph)
                .init()
                .setCTJcLeft()
                .setWidowControl(config.isWidowControl());
    }

    /**
     * 设置段落右对齐
     * @param paragraph
     */
    public static void formatParagraphRight(XWPFParagraph paragraph,DocConfig config) {
        ParagraphDecoration.newInstance(paragraph)
                .init()
                .setCTJcRight()
                .setWidowControl(config.isWidowControl());
    }

    /**
     * 格式化字体标准
     * @param run 字体
     */
    public static RunDecoration format(XWPFRun run, DocConfig config) {
        return RunDecoration.newInstance(run)
                .setFonts(config.getFontName(), config.getOtherFontName())
                .setSize(config.getFontSize());
    }

    /**
     * 格式化标题字体
     * @param run    字体
     * @param config 配置
     */
    public static void formatTitle(XWPFRun run, DocConfig config) {
        RunDecoration.newInstance(run)
                .init()
                .setFonts(config.getTitleFontName(), config.getOtherFontName())
                .setSize(config.getTitleFontSize());
    }

    /**
     * 格式化字体一级标题
     * @param run    字体
     * @param config 配置
     */
    public static void formatSerialNumber1(XWPFRun run, DocConfig config) {
        RunDecoration.newInstance(run)
                .init()
                .setFonts(config.getSerialNumber1(), config.getOtherFontName())
                .setSize(config.getFontSize());
    }

    /**
     * 格式化字体二级标题
     * @param run    字体
     * @param config 配置
     */
    public static void formatSerialNumber2(XWPFRun run, DocConfig config) {
        RunDecoration.newInstance(run)
                .init()
                .setFonts(config.getSerialNumber2(), config.getOtherFontName())
                .setSize(config.getFontSize());
    }

    /**
     * 格式化字体三级标题
     * @param run    字体
     * @param config 配置
     */
    public static void formatSerialNumber3(XWPFRun run, DocConfig config) {
        RunDecoration.newInstance(run)
                .init()
                .setFonts(config.getSerialNumber3(), config.getOtherFontName())
                .setSize(config.getFontSize());
    }

    /**
     * 格式化字体四级标题
     * @param run    字体
     * @param config 配置
     */
    public static void formatSerialNumber4(XWPFRun run, DocConfig config) {
        RunDecoration.newInstance(run)
                .init()
                .setFonts(config.getSerialNumber4(), config.getOtherFontName())
                .setSize(config.getFontSize());
    }

    /**
     * 格式化附件头字体
     * @param run
     * @param config
     */
    public static void formatSerialAttachHead(XWPFRun run, DocConfig config) {
        RunDecoration.newInstance(run)
                .init()
                .setFonts(config.getAttachHeadFontName(), config.getOtherFontName())
                .setSize(config.getAttachHeadFontSize());
    }

    /**
     * 格式化附件头
     * @param paragraph 段落
     * @param config    配置
     */
    public static void formatAttachHead(XWPFParagraph paragraph, DocConfig config) {
        ParagraphDecoration paragraphDecoration = ParagraphDecoration.newInstance(paragraph).init();
        paragraphDecoration.setWidowControl(config.isWidowControl());
        CTSpacing ctSpacing = paragraphDecoration.getCTSpacing(STLineSpacingRule.AT_LEAST, 0);
        // ctSpacing.setAfter(config.getFontSize() / 2 * 20);
        paragraphDecoration.setSpacing(ctSpacing);
        paragraphDecoration.setCTJcBoth();
        paragraphDecoration.setPageBreakBefore(true);
    }

    /**
     * 红头
     * @param paragraph 段落
     * @param config    配置
     */
    public static void formatRedParagraph(XWPFParagraph paragraph, DocConfig config) {
        ParagraphDecoration paragraphDecoration = ParagraphDecoration.newInstance(paragraph).init();
        CTSpacing ctSpacing = paragraphDecoration.getCTSpacing(STLineSpacingRule.AT_LEAST, 0);
        // ctSpacing.setAfter(config.getFontSize() / 2 * 20);
        paragraphDecoration.setSpacing(ctSpacing);
        paragraphDecoration.setCTJcBoth();
    }

    /**
     * 格式化附件
     * @param paragraph 段落
     * @param config    配置
     * @param isFirst   是否首行
     */
    public static void formatAttachExplain(XWPFParagraph paragraph, DocConfig config, Boolean isFirst) {
        String otherFontName = config.getOtherFontName();
        // 默认新罗马字体
        /*long firstLineHangingIndent  = -380L;
        long otherLineHangingIndent  = -75L;
        if (otherFontName.equals("宋体")) {
            firstLineHangingIndent = -399L;
            otherLineHangingIndent = -100L;
        }
        if (isFirst) {
            formatParagraph(paragraph, config).setFirstLineChars(firstLineHangingIndent).setLeftChars(200L);
        } else {
            formatParagraph(paragraph, config).setFirstLineChars(otherLineHangingIndent).setLeftChars(500L);
        }*/

        // 默认新罗马字体
        Integer firstLineHanging = 1195;
        Integer otherLineHanging = 240;
        if (otherFontName.equals("宋体")) {
            firstLineHanging = 1260;
            otherLineHanging = 320;
        }
        if (isFirst) {
            formatParagraph(paragraph, config).setHanging(firstLineHanging).setLeftChars(200L);
        } else {
            formatParagraph(paragraph, config).setHanging(otherLineHanging).setLeftChars(500L);
        }
    }

    /**
     * 段前插入分页符
     * @param paragraph
     */
    public static void formatPageBreakBefore(XWPFParagraph paragraph,DocConfig config) {
        ParagraphDecoration paragraphDecoration = ParagraphDecoration.newInstance(paragraph).init();
        paragraphDecoration.setWidowControl(config.isWidowControl());
        paragraphDecoration.setPageBreakBefore(true);
    }
    /**
     * 是否主标题
     * @param text 文本
     */
    public static boolean isMainTitle(String text) {
        return ReUtil.contains("(请示|报告|意见|函|便签|通知|纪要|议案|令|命令|决定|公告|通告|通报|批复|决议|公报)$", text);
    }
}
