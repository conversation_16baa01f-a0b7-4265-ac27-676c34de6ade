package com.cb.ai.data.analysis.petition.converter.model;//package com.cb.wps.docassist.converter.quickformat.model;
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 公文会议列席
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class DocMeetingLX extends BaseDoc {
//    /**
//     * 冒号位置
//     */
//    private Integer colonPos;
//    /**
//     * 标题列表
//     */
//    private List<String> texts = new ArrayList<>();
//
//    /**
//     * 标题换行数
//     */
//    public int size() {
//        return texts.size();
//    }
//}
