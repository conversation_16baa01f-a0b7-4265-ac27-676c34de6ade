package com.cb.ai.data.analysis.petition.converter.model;//package com.cb.wps.docassist.converter.quickformat.model;
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 公文标题
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class DocBodyTitle extends BaseDoc {
//    /**
//     * 标题组
//     */
//    private List<String> texts = new ArrayList<>();
//    /**
//     * 组织名称
//     */
//    private String orgName;
//}
