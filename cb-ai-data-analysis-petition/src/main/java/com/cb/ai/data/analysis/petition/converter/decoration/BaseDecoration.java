package com.cb.ai.data.analysis.petition.converter.decoration;

import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;

/**
 * 基类
 * <AUTHOR>
 */
public abstract class BaseDecoration {
    /**
     * 获取字符自动对齐方式配置
     */
    protected CTTextAlignment getCTTextAlignAuto() {
        CTTextAlignment ctTextAlignment = CTTextAlignment.Factory.newInstance();
        ctTextAlignment.setVal(STTextAlignment.AUTO);
        return ctTextAlignment;
    }

    /**
     * 初始化缩进设置
     */
    protected CTInd initCTInd() {
        CTInd ctInd = CTInd.Factory.newInstance();
        ctInd.setFirstLineChars(BigInteger.valueOf(0));
        return ctInd;
    }

    /**
     * 获取段落对齐配置
     */
    protected CTJc getCTJc(STJc.Enum val) {
        CTJc ctJc = CTJc.Factory.newInstance();
        ctJc.setVal(val);
        return ctJc;
    }

    /**
     * 获取段落间距规则
     * @param lineRule 类型
     * @param line     间距
     */
    public CTSpacing getCTSpacing(STLineSpacingRule.Enum lineRule, Object line) {
        CTSpacing ctSpacing = CTSpacing.Factory.newInstance();
        ctSpacing.setLineRule(lineRule);
        ctSpacing.setLine(line);
        return ctSpacing;
    }

    protected CTOnOff ctNil() {
        CTOnOff ctOnOff = CTOnOff.Factory.newInstance();
        ctOnOff.setNil();
        return ctOnOff;
    }

    protected CTOnOff ctOn() {
        CTOnOff ctOnOff = CTOnOff.Factory.newInstance();
        ctOnOff.setVal(true);
        return ctOnOff;
    }

    protected CTOnOff ctOff() {
        CTOnOff ctOnOff = CTOnOff.Factory.newInstance();
        ctOnOff.setVal(false);
        return ctOnOff;
    }

    /**
     * 获取字体配置
     * @param eastAsia 中文字体
     * @param ascii    英文字体
     */
    protected CTFonts getCTFonts(String eastAsia, String ascii) {
        CTFonts ctFonts = CTFonts.Factory.newInstance();
        ctFonts.setEastAsia(eastAsia);
        ctFonts.setAscii(ascii);
        ctFonts.setHAnsi(ascii);
        ctFonts.setCs(ascii);
        STHint stHint = STHint.Factory.newInstance();
        stHint.setStringValue("eastAsia");
        ctFonts.xsetHint(stHint);
        return ctFonts;
    }

    /**
     * 获取 CTHpsMeasure
     * @param obj 值
     */
    protected CTHpsMeasure getCTHpsMeasure(Object obj) {
        CTHpsMeasure ctHpsMeasure = CTHpsMeasure.Factory.newInstance();
        if (obj == null) {
            ctHpsMeasure.setNil();
        } else {
            ctHpsMeasure.setVal(obj);
        }
        return ctHpsMeasure;
    }
}
