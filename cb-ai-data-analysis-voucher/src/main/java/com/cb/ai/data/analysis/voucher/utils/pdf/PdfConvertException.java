package com.cb.ai.data.analysis.voucher.utils.pdf;

/**
 * PDF转换异常类
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2025-08-01
 */
public class PdfConvertException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     */
    public PdfConvertException() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param message 异常信息
     */
    public PdfConvertException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * 
     * @param message 异常信息
     * @param cause   异常原因
     */
    public PdfConvertException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     * 
     * @param cause 异常原因
     */
    public PdfConvertException(Throwable cause) {
        super(cause);
    }
}
