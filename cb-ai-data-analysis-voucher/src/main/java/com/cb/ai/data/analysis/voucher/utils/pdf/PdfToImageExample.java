package com.cb.ai.data.analysis.voucher.utils.pdf;

import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * PDF转图片工具类使用示例
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2025-08-01
 */
@Slf4j
public class PdfToImageExample {

    /**
     * 基本使用示例
     */
    public static void basicExample() {
        try {
            // 1. 使用默认配置转换PDF为图片
            File pdfFile = new File("C:\\Users\\<USER>\\Desktop\\pdf2img\\202401-01计提1月工资.pdf");
            List<BufferedImage> images = PdfToImageUtil.convertPdfToImages(pdfFile);

            log.info("转换完成，共生成{}张图片", images.size());

            // 2. 转换并保存为文件
            String outputDir = "C:\\Users\\<USER>\\Desktop\\pdf2img\\202401-01计提1月工资";
            List<File> imageFiles = PdfToImageUtil.convertPdfToImageFiles(pdfFile, outputDir);
            
            log.info("图片已保存到: {}", outputDir);
            for (File imageFile : imageFiles) {
                log.info("生成图片: {}", imageFile.getName());
            }
            
        } catch (PdfConvertException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 高级配置示例
     */
    public static void advancedExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");
            
            // 1. 使用高质量配置
            PdfToImageConfig highQualityConfig = PdfToImageConfig.highQualityConfig();
            List<BufferedImage> highQualityImages = PdfToImageUtil.convertPdfToImages(pdfFile, highQualityConfig);
            log.info("高质量转换完成，共{}张图片", highQualityImages.size());
            
            // 2. 使用低质量配置（适合预览）
            PdfToImageConfig lowQualityConfig = PdfToImageConfig.lowQualityConfig();
            List<BufferedImage> lowQualityImages = PdfToImageUtil.convertPdfToImages(pdfFile, lowQualityConfig);
            log.info("低质量转换完成，共{}张图片", lowQualityImages.size());
            
            // 3. 自定义配置
            PdfToImageConfig customConfig = new PdfToImageConfig();
            customConfig.setDpi(200.0f);           // 设置DPI
            customConfig.setFormat("JPEG");        // 设置格式
            customConfig.setQuality(0.8f);         // 设置JPEG质量
            customConfig.setAntiAliasing(true);    // 启用抗锯齿
            
            String outputDir = "path/to/custom/output";
            List<File> customImages = PdfToImageUtil.convertPdfToImageFiles(pdfFile, outputDir, customConfig);
            log.info("自定义配置转换完成，生成{}个文件", customImages.size());
            
        } catch (PdfConvertException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 转换为字节数组示例
     */
    public static void bytesExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 转换为字节数组（可用于网络传输或存储到数据库）
            List<byte[]> imageBytes = PdfToImageUtil.convertPdfToImageBytes(pdfFile);

            log.info("转换为字节数组完成，共{}张图片", imageBytes.size());

            for (int i = 0; i < imageBytes.size(); i++) {
                byte[] bytes = imageBytes.get(i);
                log.info("第{}页图片大小: {} bytes", i + 1, bytes.length);

                // 这里可以将字节数组保存到数据库或通过网络传输
                // saveToDatabase(bytes);
                // sendOverNetwork(bytes);
            }

        } catch (PdfConvertException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 转换为Base64字符串示例
     */
    public static void base64Example() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 转换为Base64字符串（使用默认配置）
            List<String> base64Strings = PdfToImageUtil.convertPdfToBase64(pdfFile);

            log.info("转换为Base64完成，共{}张图片", base64Strings.size());

            for (int i = 0; i < base64Strings.size(); i++) {
                String base64 = base64Strings.get(i);
                log.info("第{}页Base64长度: {} 字符", i + 1, base64.length());
                log.debug("第{}页Base64前缀: {}", i + 1, base64.substring(0, Math.min(50, base64.length())));

                // 这里可以将Base64字符串用于前端显示或API传输
                // sendToFrontend(base64);
                // saveToJson(base64);
            }

            // 2. 使用自定义配置转换为Base64
            PdfToImageConfig config = new PdfToImageConfig();
            config.setDpi(150.0f);
            config.setFormat("JPEG");
            config.setQuality(0.8f);

            List<String> customBase64 = PdfToImageUtil.convertPdfToBase64(pdfFile, config);
            log.info("自定义配置Base64转换完成，共{}张图片", customBase64.size());

        } catch (PdfConvertException e) {
            log.error("PDF转Base64失败", e);
        }
    }

    /**
     * 转换为数据URI示例
     */
    public static void dataUriExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 转换为数据URI（可直接用于HTML img标签）
            List<String> dataUris = PdfToImageUtil.convertPdfToDataUri(pdfFile);

            log.info("转换为数据URI完成，共{}张图片", dataUris.size());

            for (int i = 0; i < dataUris.size(); i++) {
                String dataUri = dataUris.get(i);
                log.info("第{}页数据URI长度: {} 字符", i + 1, dataUri.length());
                log.debug("第{}页数据URI前缀: {}", i + 1, dataUri.substring(0, Math.min(100, dataUri.length())));

                // 数据URI可以直接用于HTML
                // String html = "<img src=\"" + dataUri + "\" alt=\"PDF Page " + (i + 1) + "\">";
                // log.info("HTML标签: {}", html);
            }

            // 2. 使用JPEG格式的数据URI
            PdfToImageConfig jpegConfig = new PdfToImageConfig();
            jpegConfig.setFormat("JPEG");
            jpegConfig.setQuality(0.9f);

            List<String> jpegDataUris = PdfToImageUtil.convertPdfToDataUri(pdfFile, jpegConfig);
            log.info("JPEG格式数据URI转换完成，共{}张图片", jpegDataUris.size());

        } catch (PdfConvertException e) {
            log.error("PDF转数据URI失败", e);
        }
    }

    /**
     * 批量处理示例
     */
    public static void batchProcessExample() {
        String inputDir = "path/to/pdf/directory";
        String outputDir = "path/to/output/directory";
        
        File inputDirectory = new File(inputDir);
        if (!inputDirectory.exists() || !inputDirectory.isDirectory()) {
            log.error("输入目录不存在: {}", inputDir);
            return;
        }
        
        File[] pdfFiles = inputDirectory.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".pdf"));
        
        if (pdfFiles == null || pdfFiles.length == 0) {
            log.info("没有找到PDF文件");
            return;
        }
        
        PdfToImageConfig config = PdfToImageConfig.defaultConfig();
        
        for (File pdfFile : pdfFiles) {
            try {
                log.info("开始处理: {}", pdfFile.getName());
                
                // 为每个PDF创建单独的输出目录
                String pdfOutputDir = outputDir + File.separator + 
                    pdfFile.getName().replaceAll("\\.pdf$", "");
                
                List<File> imageFiles = PdfToImageUtil.convertPdfToImageFiles(
                    pdfFile, pdfOutputDir, config);
                
                log.info("完成处理: {}，生成{}张图片", pdfFile.getName(), imageFiles.size());
                
            } catch (PdfConvertException e) {
                log.error("处理文件失败: {}", pdfFile.getName(), e);
            }
        }
    }

    /**
     * 错误处理示例
     */
    public static void errorHandlingExample() {
        try {
            // 1. 处理不存在的文件
            File nonExistentFile = new File("non_existent.pdf");
            PdfToImageUtil.convertPdfToImages(nonExistentFile);
            
        } catch (PdfConvertException e) {
            log.error("预期的错误 - 文件不存在: {}", e.getMessage());
        }
        
        try {
            // 2. 处理无效配置
            PdfToImageConfig invalidConfig = new PdfToImageConfig();
            invalidConfig.setDpi(-1); // 无效的DPI
            invalidConfig.validate();
            
        } catch (IllegalArgumentException e) {
            log.error("预期的错误 - 无效配置: {}", e.getMessage());
        }
        
        try {
            // 3. 处理不支持的格式
            PdfToImageConfig invalidFormatConfig = new PdfToImageConfig();
            invalidFormatConfig.setFormat("UNSUPPORTED");
            invalidFormatConfig.validate();
            
        } catch (IllegalArgumentException e) {
            log.error("预期的错误 - 不支持的格式: {}", e.getMessage());
        }
    }

    /**
     * 性能测试示例
     */
    public static void performanceExample() {
        File pdfFile = new File("path/to/large/document.pdf");
        
        if (!pdfFile.exists()) {
            log.warn("测试文件不存在: {}", pdfFile.getAbsolutePath());
            return;
        }
        
        // 测试不同DPI的性能
        float[] dpiValues = {72.0f, 150.0f, 300.0f};
        
        for (float dpi : dpiValues) {
            PdfToImageConfig config = new PdfToImageConfig();
            config.setDpi(dpi);
            
            long startTime = System.currentTimeMillis();
            
            try {
                List<BufferedImage> images = PdfToImageUtil.convertPdfToImages(pdfFile, config);
                long endTime = System.currentTimeMillis();
                
                log.info("DPI: {}, 页数: {}, 耗时: {}ms", 
                    dpi, images.size(), endTime - startTime);
                
            } catch (PdfConvertException e) {
                log.error("DPI {} 转换失败", dpi, e);
            }
        }
    }

    /**
     * 主方法 - 运行示例
     */
    public static void main(String[] args) {
        log.info("=== PDF转图片工具类使用示例 ===");
        
        // 注意：运行前请修改文件路径为实际存在的PDF文件
        
        log.info("1. 基本使用示例");
         basicExample();
        
        log.info("2. 高级配置示例");
        // advancedExample();
        
        log.info("3. 字节数组示例");
        // bytesExample();
        
        log.info("4. 批量处理示例");
        // batchProcessExample();
        
        log.info("5. 错误处理示例");
        // errorHandlingExample();
        
        log.info("6. 性能测试示例");
        // performanceExample();
        
        log.info("=== 示例运行完成 ===");
    }

    /**
     * 主方法 - 运行示例
     */
    public static void main(String[] args) {
        log.info("=== PDF转图片工具类使用示例 ===");

        // 注意：运行前请修改文件路径为实际存在的PDF文件

        log.info("1. 基本使用示例");
        // basicExample();

        log.info("2. 高级配置示例");
        // advancedExample();

        log.info("3. 字节数组示例");
        // bytesExample();

        log.info("4. Base64字符串示例");
        // base64Example();

        log.info("5. 数据URI示例");
        // dataUriExample();

        log.info("6. 批量处理示例");
        // batchProcessExample();

        log.info("7. 错误处理示例");
        errorHandlingExample();

        log.info("8. 性能测试示例");
        // performanceExample();

        log.info("=== 示例运行完成 ===");
    }
}
