package com.cb.ai.data.analysis.voucher.utils.pdf;

import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * PDF转图片工具类使用示例 - 新版Builder模式API
 *
 * <AUTHOR> Assistant
 * @version 2.0
 * @since 2025-08-01
 */
@Slf4j
public class PdfToImageExample {

    /**
     * 基本使用示例 - 新版API
     */
    public static void basicExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 获取PDF页数
            int pageCount = PdfToImageUtil.getPageCount(pdfFile);
            log.info("PDF总页数: {}", pageCount);

            // 2. 转换所有页面为图片
            List<BufferedImage> images = PdfToImageUtil.from(pdfFile).toImages();
            log.info("转换完成，共生成{}张图片", images.size());

            // 3. 转换并保存为文件
            List<File> imageFiles = PdfToImageUtil.from(pdfFile).toFiles("output/");
            log.info("图片已保存，共{}个文件", imageFiles.size());

        } catch (PdfConvertException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 单页转换示例
     */
    public static void singlePageExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 转换第一页为图片
            BufferedImage firstPage = PdfToImageUtil.from(pdfFile).page(0).toImage();
            log.info("第一页图片尺寸: {}x{}", firstPage.getWidth(), firstPage.getHeight());

            // 2. 转换第一页并保存为文件
            List<File> files = PdfToImageUtil.from(pdfFile).page(0).toFiles("output/");
            log.info("第一页已保存: {}", files.get(0).getName());

            // 3. 转换第一页为Base64
            List<String> base64List = PdfToImageUtil.from(pdfFile).page(0).toBase64();
            log.info("第一页Base64长度: {} 字符", base64List.get(0).length());

            // 4. 转换第一页为数据URI
            List<String> dataUriList = PdfToImageUtil.from(pdfFile).page(0).toDataUri();
            log.info("第一页数据URI长度: {} 字符", dataUriList.get(0).length());

        } catch (PdfConvertException e) {
            log.error("单页转换失败", e);
        }
    }

    /**
     * 页面范围转换示例
     */
    public static void pageRangeExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 转换前5页
            List<BufferedImage> firstFivePages = PdfToImageUtil.from(pdfFile)
                .pages(0, 4)  // 页面索引从0开始
                .toImages();
            log.info("前5页转换完成，共{}张图片", firstFivePages.size());

            // 2. 转换指定范围并保存为文件
            List<File> rangeFiles = PdfToImageUtil.from(pdfFile)
                .pages(2, 7)  // 转换第3-8页
                .toFiles("output/range/");
            log.info("页面范围转换完成，生成{}个文件", rangeFiles.size());

        } catch (PdfConvertException e) {
            log.error("页面范围转换失败", e);
        }
    }

    /**
     * 高级配置示例 - 新版API
     */
    public static void advancedExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 使用高质量预设
            List<BufferedImage> highQualityImages = PdfToImageUtil.from(pdfFile)
                .highQuality()
                .toImages();
            log.info("高质量转换完成，共{}张图片", highQualityImages.size());

            // 2. 使用低质量预设（适合预览）
            List<BufferedImage> lowQualityImages = PdfToImageUtil.from(pdfFile)
                .lowQuality()
                .toImages();
            log.info("低质量转换完成，共{}张图片", lowQualityImages.size());

            // 3. 链式调用自定义配置
            List<File> customFiles = PdfToImageUtil.from(pdfFile)
                .dpi(200.0f)           // 设置DPI
                .format("JPEG")        // 设置格式
                .quality(0.8f)         // 设置JPEG质量
                .antiAliasing(true)    // 启用抗锯齿
                .toFiles("output/custom/");
            log.info("自定义配置转换完成，生成{}个文件", customFiles.size());

            // 4. 内存优化配置
            List<String> base64List = PdfToImageUtil.from(pdfFile)
                .memoryOptimized()
                .toBase64();
            log.info("内存优化转换完成，共{}页", base64List.size());

        } catch (PdfConvertException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 转换为字节数组示例
     */
    public static void bytesExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 转换为字节数组（可用于网络传输或存储到数据库）
            List<byte[]> imageBytes = PdfToImageUtil.convertPdfToImageBytes(pdfFile);

            log.info("转换为字节数组完成，共{}张图片", imageBytes.size());

            for (int i = 0; i < imageBytes.size(); i++) {
                byte[] bytes = imageBytes.get(i);
                log.info("第{}页图片大小: {} bytes", i + 1, bytes.length);

                // 这里可以将字节数组保存到数据库或通过网络传输
                // saveToDatabase(bytes);
                // sendOverNetwork(bytes);
            }

        } catch (PdfConvertException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 转换为Base64字符串示例
     */
    public static void base64Example() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 转换为Base64字符串（使用默认配置）
            List<String> base64Strings = PdfToImageUtil.convertPdfToBase64(pdfFile);

            log.info("转换为Base64完成，共{}张图片", base64Strings.size());

            for (int i = 0; i < base64Strings.size(); i++) {
                String base64 = base64Strings.get(i);
                log.info("第{}页Base64长度: {} 字符", i + 1, base64.length());
                log.debug("第{}页Base64前缀: {}", i + 1, base64.substring(0, Math.min(50, base64.length())));

                // 这里可以将Base64字符串用于前端显示或API传输
                // sendToFrontend(base64);
                // saveToJson(base64);
            }

            // 2. 使用自定义配置转换为Base64
            PdfToImageConfig config = new PdfToImageConfig();
            config.setDpi(150.0f);
            config.setFormat("JPEG");
            config.setQuality(0.8f);

            List<String> customBase64 = PdfToImageUtil.convertPdfToBase64(pdfFile, config);
            log.info("自定义配置Base64转换完成，共{}张图片", customBase64.size());

        } catch (PdfConvertException e) {
            log.error("PDF转Base64失败", e);
        }
    }

    /**
     * 分页处理示例（内存友好）- 新版API
     */
    public static void pagedProcessingExample() {
        try {
            File pdfFile = new File("path/to/large/document.pdf");

            if (!pdfFile.exists()) {
                log.warn("测试文件不存在: {}", pdfFile.getAbsolutePath());
                return;
            }

            int totalPages = PdfToImageUtil.getPageCount(pdfFile);
            log.info("大型PDF文件总页数: {}", totalPages);

            // 1. 分页转换为文件（每次处理5页，避免内存溢出）
            List<File> imageFiles = PdfToImageUtil.from(pdfFile)
                .pageSize(5)
                .toFilesPaged("output/paged/");
            log.info("分页转换完成，生成{}个图片文件", imageFiles.size());

            // 2. 分页转换为Base64（每次处理3页）
            List<String> base64Strings = PdfToImageUtil.from(pdfFile)
                .pageSize(3)
                .toBase64Paged();
            log.info("分页Base64转换完成，共{}页", base64Strings.size());

            // 3. 逐页处理（最节省内存的方式）
            log.info("开始逐页处理...");
            for (int i = 0; i < Math.min(totalPages, 10); i++) { // 只处理前10页作为示例
                try {
                    // 逐页转换，立即处理，不在内存中累积
                    List<String> pageBase64 = PdfToImageUtil.from(pdfFile)
                        .page(i)
                        .toBase64();
                    log.info("第{}页Base64长度: {} 字符", i + 1, pageBase64.get(0).length());

                    // 处理完一页后可以立即使用或保存
                    // processPageData(pageBase64.get(0));

                } catch (PdfConvertException e) {
                    log.error("处理第{}页失败", i + 1, e);
                }
            }

        } catch (PdfConvertException e) {
            log.error("分页处理失败", e);
        }
    }

    /**
     * Base64和数据URI示例 - 新版API
     */
    public static void base64AndDataUriExample() {
        try {
            File pdfFile = new File("path/to/your/document.pdf");

            // 1. 转换为Base64字符串
            List<String> base64Strings = PdfToImageUtil.from(pdfFile).toBase64();
            log.info("转换为Base64完成，共{}张图片", base64Strings.size());

            for (int i = 0; i < base64Strings.size(); i++) {
                String base64 = base64Strings.get(i);
                log.info("第{}页Base64长度: {} 字符", i + 1, base64.length());
            }

            // 2. 转换为数据URI（可直接用于HTML img标签）
            List<String> dataUris = PdfToImageUtil.from(pdfFile).toDataUri();
            log.info("转换为数据URI完成，共{}张图片", dataUris.size());

            for (int i = 0; i < dataUris.size(); i++) {
                String dataUri = dataUris.get(i);
                log.info("第{}页数据URI长度: {} 字符", i + 1, dataUri.length());

                // 数据URI可以直接用于HTML
                // String html = "<img src=\"" + dataUri + "\" alt=\"PDF Page " + (i + 1) + "\">";
            }

            // 3. 使用JPEG格式的数据URI
            List<String> jpegDataUris = PdfToImageUtil.from(pdfFile)
                .format("JPEG")
                .quality(0.9f)
                .toDataUri();
            log.info("JPEG格式数据URI转换完成，共{}张图片", jpegDataUris.size());

        } catch (PdfConvertException e) {
            log.error("PDF转Base64/数据URI失败", e);
        }
    }

    /**
     * 批量处理示例
     */
    public static void batchProcessExample() {
        String inputDir = "path/to/pdf/directory";
        String outputDir = "path/to/output/directory";
        
        File inputDirectory = new File(inputDir);
        if (!inputDirectory.exists() || !inputDirectory.isDirectory()) {
            log.error("输入目录不存在: {}", inputDir);
            return;
        }
        
        File[] pdfFiles = inputDirectory.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".pdf"));
        
        if (pdfFiles == null || pdfFiles.length == 0) {
            log.info("没有找到PDF文件");
            return;
        }
        
        PdfToImageConfig config = PdfToImageConfig.defaultConfig();
        
        for (File pdfFile : pdfFiles) {
            try {
                log.info("开始处理: {}", pdfFile.getName());
                
                // 为每个PDF创建单独的输出目录
                String pdfOutputDir = outputDir + File.separator + 
                    pdfFile.getName().replaceAll("\\.pdf$", "");
                
                List<File> imageFiles = PdfToImageUtil.convertPdfToImageFiles(
                    pdfFile, pdfOutputDir, config);
                
                log.info("完成处理: {}，生成{}张图片", pdfFile.getName(), imageFiles.size());
                
            } catch (PdfConvertException e) {
                log.error("处理文件失败: {}", pdfFile.getName(), e);
            }
        }
    }

    /**
     * 错误处理示例
     */
    public static void errorHandlingExample() {
        try {
            // 1. 处理不存在的文件
            File nonExistentFile = new File("non_existent.pdf");
            PdfToImageUtil.convertPdfToImages(nonExistentFile);
            
        } catch (PdfConvertException e) {
            log.error("预期的错误 - 文件不存在: {}", e.getMessage());
        }
        
        try {
            // 2. 处理无效配置
            PdfToImageConfig invalidConfig = new PdfToImageConfig();
            invalidConfig.setDpi(-1); // 无效的DPI
            invalidConfig.validate();
            
        } catch (IllegalArgumentException e) {
            log.error("预期的错误 - 无效配置: {}", e.getMessage());
        }
        
        try {
            // 3. 处理不支持的格式
            PdfToImageConfig invalidFormatConfig = new PdfToImageConfig();
            invalidFormatConfig.setFormat("UNSUPPORTED");
            invalidFormatConfig.validate();
            
        } catch (IllegalArgumentException e) {
            log.error("预期的错误 - 不支持的格式: {}", e.getMessage());
        }
    }

    /**
     * 性能测试示例
     */
    public static void performanceExample() {
        File pdfFile = new File("path/to/large/document.pdf");
        
        if (!pdfFile.exists()) {
            log.warn("测试文件不存在: {}", pdfFile.getAbsolutePath());
            return;
        }
        
        // 测试不同DPI的性能
        float[] dpiValues = {72.0f, 150.0f, 300.0f};
        
        for (float dpi : dpiValues) {
            PdfToImageConfig config = new PdfToImageConfig();
            config.setDpi(dpi);
            
            long startTime = System.currentTimeMillis();
            
            try {
                List<BufferedImage> images = PdfToImageUtil.convertPdfToImages(pdfFile, config);
                long endTime = System.currentTimeMillis();
                
                log.info("DPI: {}, 页数: {}, 耗时: {}ms", 
                    dpi, images.size(), endTime - startTime);
                
            } catch (PdfConvertException e) {
                log.error("DPI {} 转换失败", dpi, e);
            }
        }
    }

    /**
     * 性能对比示例
     */
    public static void performanceExample() {
        File pdfFile = new File("path/to/large/document.pdf");

        if (!pdfFile.exists()) {
            log.warn("测试文件不存在: {}", pdfFile.getAbsolutePath());
            return;
        }

        // 测试不同DPI的性能
        float[] dpiValues = {72.0f, 150.0f, 300.0f};

        for (float dpi : dpiValues) {
            long startTime = System.currentTimeMillis();

            try {
                List<BufferedImage> images = PdfToImageUtil.from(pdfFile)
                    .dpi(dpi)
                    .toImages();
                long endTime = System.currentTimeMillis();

                log.info("DPI: {}, 页数: {}, 耗时: {}ms",
                    dpi, images.size(), endTime - startTime);

            } catch (PdfConvertException e) {
                log.error("DPI {} 转换失败", dpi, e);
            }
        }
    }

    /**
     * 错误处理示例
     */
    public static void errorHandlingExample() {
        try {
            // 1. 处理不存在的文件
            File nonExistentFile = new File("non_existent.pdf");
            PdfToImageUtil.from(nonExistentFile).toImages();

        } catch (PdfConvertException e) {
            log.error("预期的错误 - 文件不存在: {}", e.getMessage());
        }

        try {
            // 2. 处理页面索引超出范围
            File pdfFile = new File("path/to/your/document.pdf");
            if (pdfFile.exists()) {
                PdfToImageUtil.from(pdfFile).page(999).toImage(); // 假设页面不存在
            }

        } catch (PdfConvertException e) {
            log.error("预期的错误 - 页面索引超出范围: {}", e.getMessage());
        }
    }

    /**
     * 主方法 - 运行示例
     */
    public static void main(String[] args) {
        log.info("=== PDF转图片工具类使用示例 - 新版Builder API ===");

        // 注意：运行前请修改文件路径为实际存在的PDF文件

        log.info("1. 基本使用示例");
        // basicExample();

        log.info("2. 单页转换示例");
        // singlePageExample();

        log.info("3. 页面范围转换示例");
        // pageRangeExample();

        log.info("4. 高级配置示例");
        // advancedExample();

        log.info("5. 分页处理示例（内存友好）");
        // pagedProcessingExample();

        log.info("6. Base64和数据URI示例");
        // base64AndDataUriExample();

        log.info("7. 错误处理示例");
        errorHandlingExample();

        log.info("8. 性能测试示例");
        // performanceExample();

        log.info("=== 示例运行完成 ===");

        // 展示API的简洁性
        log.info("\n=== API简洁性展示 ===");
        log.info("// 获取页数");
        log.info("int pages = PdfToImageUtil.getPageCount(pdfFile);");
        log.info("");
        log.info("// 转换单页");
        log.info("BufferedImage image = PdfToImageUtil.from(pdfFile).page(0).toImage();");
        log.info("");
        log.info("// 高质量转换并保存");
        log.info("List<File> files = PdfToImageUtil.from(pdfFile).highQuality().toFiles(\"output/\");");
        log.info("");
        log.info("// 分页处理大文件");
        log.info("List<String> base64 = PdfToImageUtil.from(pdfFile).pageSize(5).toBase64Paged();");
    }
}
