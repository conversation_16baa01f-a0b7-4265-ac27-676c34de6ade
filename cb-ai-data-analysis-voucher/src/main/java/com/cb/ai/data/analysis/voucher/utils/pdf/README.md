# PDF转图片工具类 (PdfToImageUtil)

## 概述

PdfToImageUtil 是一个功能强大的PDF转图片工具类，支持将PDF文件转换为各种格式的图片。该工具类经过优化，特别适合处理大型PDF文件，避免内存溢出问题。

## 主要特性

### 🚀 核心功能
- **获取PDF页数**: 快速获取PDF文件的总页数
- **单页转换**: 转换PDF的指定页面为图片
- **范围转换**: 转换PDF的指定页面范围
- **全文档转换**: 转换整个PDF文档
- **分页处理**: 内存友好的分批处理大型PDF文件

### 📸 输出格式
- **BufferedImage**: Java图像对象
- **图片文件**: PNG、JPEG、BMP、GIF等格式
- **字节数组**: 用于网络传输或数据库存储
- **Base64字符串**: 用于API传输或JSON嵌入
- **数据URI**: 可直接用于HTML img标签

### ⚙️ 配置选项
- **DPI设置**: 72-300 DPI，支持自定义
- **图片格式**: PNG、JPEG、BMP、GIF
- **图片质量**: JPEG格式支持质量控制
- **抗锯齿**: 可选的图像质量增强
- **分页大小**: 控制每批处理的页数
- **内存优化**: 自动垃圾回收控制

## 快速开始

### 基本使用

```java
// 1. 获取PDF总页数
File pdfFile = new File("document.pdf");
int totalPages = PdfToImageUtil.getPdfPageCount(pdfFile);

// 2. 转换单页为图片
BufferedImage image = PdfToImageUtil.convertPdfPageToImage(pdfFile, 0); // 第一页

// 3. 转换单页为Base64
String base64 = PdfToImageUtil.convertPdfPageToBase64(pdfFile, 0);

// 4. 转换页面范围
List<BufferedImage> images = PdfToImageUtil.convertPdfPagesToImages(pdfFile, 0, 4); // 前5页
```

### 内存友好的大文件处理

```java
// 分页转换大型PDF文件（每次处理5页）
List<File> imageFiles = PdfToImageUtil.convertPdfToImageFilesPaged(
    pdfFile, "output/", 5);

// 分页转换为Base64
List<String> base64Strings = PdfToImageUtil.convertPdfToBase64Paged(
    pdfFile, 3); // 每次处理3页
```

### 自定义配置

```java
// 高质量配置
PdfToImageConfig highQuality = PdfToImageConfig.highQualityConfig();

// 内存优化配置
PdfToImageConfig memoryOptimized = PdfToImageConfig.memoryOptimizedConfig();

// 自定义配置
PdfToImageConfig custom = new PdfToImageConfig();
custom.setDpi(200.0f);
custom.setFormat("JPEG");
custom.setQuality(0.8f);
custom.setPageSize(5);
custom.setAutoGc(true);

BufferedImage image = PdfToImageUtil.convertPdfPageToImage(pdfFile, 0, custom);
```

## API 参考

### 页数获取
- `getPdfPageCount(File pdfFile)`: 获取PDF文件总页数
- `getPdfPageCount(InputStream inputStream)`: 获取PDF输入流总页数

### 单页转换
- `convertPdfPageToImage(File pdfFile, int pageIndex)`: 转换为BufferedImage
- `convertPdfPageToImageFile(File pdfFile, int pageIndex, File outputFile)`: 转换并保存为文件
- `convertPdfPageToImageBytes(File pdfFile, int pageIndex)`: 转换为字节数组
- `convertPdfPageToBase64(File pdfFile, int pageIndex)`: 转换为Base64字符串
- `convertPdfPageToDataUri(File pdfFile, int pageIndex)`: 转换为数据URI

### 范围转换
- `convertPdfPagesToImages(File pdfFile, int startPage, int endPage)`: 转换页面范围

### 全文档转换
- `convertPdfToImages(File pdfFile)`: 转换所有页面为BufferedImage列表
- `convertPdfToImageFiles(File pdfFile, String outputDir)`: 转换并保存为文件
- `convertPdfToImageBytes(File pdfFile)`: 转换为字节数组列表
- `convertPdfToBase64(File pdfFile)`: 转换为Base64字符串列表
- `convertPdfToDataUri(File pdfFile)`: 转换为数据URI列表

### 分页处理（内存友好）
- `convertPdfToImageFilesPaged(File pdfFile, String outputDir, int pageSize)`: 分页转换为文件
- `convertPdfToBase64Paged(File pdfFile, int pageSize)`: 分页转换为Base64

## 配置选项

### 预设配置
- `PdfToImageConfig.defaultConfig()`: 默认配置（150 DPI, PNG格式）
- `PdfToImageConfig.highQualityConfig()`: 高质量配置（300 DPI, PNG格式）
- `PdfToImageConfig.lowQualityConfig()`: 低质量配置（72 DPI, JPEG格式）
- `PdfToImageConfig.memoryOptimizedConfig()`: 内存优化配置

### 自定义参数
- `dpi`: 图片分辨率（72-300）
- `format`: 图片格式（PNG、JPEG、BMP、GIF）
- `quality`: JPEG质量（0.0-1.0）
- `antiAliasing`: 是否启用抗锯齿
- `pageSize`: 分页处理时每批页数
- `autoGc`: 是否自动垃圾回收

## 性能建议

### 内存优化
1. **使用分页处理**: 对于大型PDF文件，使用 `convertPdfToImageFilesPaged` 等分页方法
2. **合理设置页面大小**: 根据可用内存调整 `pageSize` 参数
3. **选择合适的格式**: JPEG格式比PNG占用更少内存
4. **关闭抗锯齿**: 对于大批量处理，可关闭抗锯齿以节省内存

### 性能优化
1. **降低DPI**: 对于预览用途，使用72-150 DPI
2. **批量处理**: 使用范围转换而不是逐页转换
3. **复用配置**: 避免重复创建配置对象

## 错误处理

工具类提供完善的错误处理机制：

```java
try {
    BufferedImage image = PdfToImageUtil.convertPdfPageToImage(pdfFile, 0);
} catch (PdfConvertException e) {
    log.error("PDF转换失败: {}", e.getMessage(), e);
}
```

常见异常：
- `PdfConvertException`: PDF转换相关异常
- `IllegalArgumentException`: 参数验证异常

## 依赖

- Apache PDFBox 2.0.29
- Hutool（Base64编码、文件操作）
- SLF4J（日志记录）
- Lombok（简化代码）

## 示例代码

详细的使用示例请参考 `PdfToImageExample.java` 文件，包含：
- 基本使用示例
- 高级配置示例
- 单页转换示例
- 分页处理示例
- 错误处理示例
- 性能测试示例

## 注意事项

1. **文件路径**: 确保PDF文件路径正确且文件存在
2. **权限**: 确保有读取PDF文件和写入输出目录的权限
3. **内存**: 处理大型PDF时建议使用分页方法
4. **格式**: 确保输出格式受支持
5. **页面索引**: 页面索引从0开始计算

## 更新日志

### v2.0.0 (2025-08-01)
- ✨ 新增获取PDF总页数功能
- ✨ 新增单页转换功能
- ✨ 新增页面范围转换功能
- ✨ 新增分页处理功能（内存友好）
- ✨ 新增Base64和数据URI转换功能
- 🚀 优化内存使用，支持大型PDF文件处理
- 📝 完善文档和示例代码

### v1.0.0
- 🎉 初始版本，支持基本的PDF转图片功能
