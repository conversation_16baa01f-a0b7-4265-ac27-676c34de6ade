# PDF转图片工具类 (PdfToImageUtil) v2.0

## 概述

PdfToImageUtil 是一个功能强大的PDF转图片工具类，采用**Builder模式**和**链式调用**设计，提供简洁易用的API。该工具类经过优化，特别适合处理大型PDF文件，避免内存溢出问题。

## 🎯 设计理念

- **简洁易用**: 采用Builder模式，链式调用，API清晰直观
- **内存友好**: 支持分页处理，避免大文件内存溢出
- **功能丰富**: 支持多种输出格式和配置选项
- **性能优化**: 提供多种预设配置，满足不同性能需求

## 主要特性

### 🚀 核心功能
- **获取PDF页数**: 快速获取PDF文件的总页数
- **单页转换**: 转换PDF的指定页面为图片
- **范围转换**: 转换PDF的指定页面范围
- **全文档转换**: 转换整个PDF文档
- **分页处理**: 内存友好的分批处理大型PDF文件

### 📸 输出格式
- **BufferedImage**: Java图像对象
- **图片文件**: PNG、JPEG、BMP、GIF等格式
- **字节数组**: 用于网络传输或数据库存储
- **Base64字符串**: 用于API传输或JSON嵌入
- **数据URI**: 可直接用于HTML img标签

### ⚙️ 配置选项
- **DPI设置**: 72-300 DPI，支持自定义
- **图片格式**: PNG、JPEG、BMP、GIF
- **图片质量**: JPEG格式支持质量控制
- **抗锯齿**: 可选的图像质量增强
- **分页大小**: 控制每批处理的页数
- **内存优化**: 自动垃圾回收控制

## 快速开始

### 基本使用

```java
// 1. 获取PDF总页数
File pdfFile = new File("document.pdf");
int totalPages = PdfToImageUtil.getPageCount(pdfFile);

// 2. 转换所有页面为图片
List<BufferedImage> images = PdfToImageUtil.from(pdfFile).toImages();

// 3. 转换并保存为文件
List<File> files = PdfToImageUtil.from(pdfFile).toFiles("output/");

// 4. 转换为Base64字符串
List<String> base64List = PdfToImageUtil.from(pdfFile).toBase64();
```

### 单页和范围转换

```java
// 转换单页
BufferedImage image = PdfToImageUtil.from(pdfFile).page(0).toImage();

// 转换页面范围
List<BufferedImage> images = PdfToImageUtil.from(pdfFile).pages(0, 4).toImages();

// 单页转换为Base64
List<String> base64 = PdfToImageUtil.from(pdfFile).page(0).toBase64();
```

### 链式配置

```java
// 高质量转换
List<File> files = PdfToImageUtil.from(pdfFile)
    .highQuality()
    .toFiles("output/");

// 自定义配置
List<String> base64List = PdfToImageUtil.from(pdfFile)
    .dpi(200.0f)
    .format("JPEG")
    .quality(0.8f)
    .antiAliasing(true)
    .toBase64();
```

### 内存友好的大文件处理

```java
// 分页转换大型PDF文件
List<File> files = PdfToImageUtil.from(pdfFile)
    .pageSize(5)
    .toFilesPaged("output/");

// 分页转换为Base64
List<String> base64List = PdfToImageUtil.from(pdfFile)
    .memoryOptimized()
    .toBase64Paged();

// 逐页处理（最节省内存）
for (int i = 0; i < totalPages; i++) {
    String pageBase64 = PdfToImageUtil.from(pdfFile)
        .page(i)
        .toBase64()
        .get(0);
    // 立即处理，不在内存中累积
}
```

## API 参考

### 入口方法
- `PdfToImageUtil.from(File pdfFile)`: 创建PDF转换器（文件输入）
- `PdfToImageUtil.from(InputStream inputStream)`: 创建PDF转换器（流输入）
- `PdfToImageUtil.getPageCount(File pdfFile)`: 快速获取PDF页数

### 配置方法（链式调用）
- `.dpi(float dpi)`: 设置DPI
- `.format(String format)`: 设置图片格式
- `.quality(float quality)`: 设置JPEG质量
- `.antiAliasing(boolean enable)`: 设置抗锯齿
- `.pageSize(int size)`: 设置分页大小
- `.autoGc(boolean enable)`: 设置自动垃圾回收

### 页面选择方法
- `.page(int pageIndex)`: 选择单个页面
- `.pages(int startPage, int endPage)`: 选择页面范围

### 预设配置方法
- `.highQuality()`: 高质量预设（300 DPI, PNG）
- `.lowQuality()`: 低质量预设（72 DPI, JPEG）
- `.memoryOptimized()`: 内存优化预设

### 转换方法
- `.toImage()`: 转换为单个BufferedImage（仅单页）
- `.toImages()`: 转换为BufferedImage列表
- `.toFiles(String outputDir)`: 转换并保存为文件
- `.toBytes()`: 转换为字节数组列表
- `.toBase64()`: 转换为Base64字符串列表
- `.toDataUri()`: 转换为数据URI列表

### 分页处理方法（内存友好）
- `.toFilesPaged(String outputDir)`: 分页转换为文件
- `.toBase64Paged()`: 分页转换为Base64

## 配置选项

### 预设配置
- `PdfToImageConfig.defaultConfig()`: 默认配置（150 DPI, PNG格式）
- `PdfToImageConfig.highQualityConfig()`: 高质量配置（300 DPI, PNG格式）
- `PdfToImageConfig.lowQualityConfig()`: 低质量配置（72 DPI, JPEG格式）
- `PdfToImageConfig.memoryOptimizedConfig()`: 内存优化配置

### 自定义参数
- `dpi`: 图片分辨率（72-300）
- `format`: 图片格式（PNG、JPEG、BMP、GIF）
- `quality`: JPEG质量（0.0-1.0）
- `antiAliasing`: 是否启用抗锯齿
- `pageSize`: 分页处理时每批页数
- `autoGc`: 是否自动垃圾回收

## 性能建议

### 内存优化
1. **使用分页处理**: 对于大型PDF文件，使用 `convertPdfToImageFilesPaged` 等分页方法
2. **合理设置页面大小**: 根据可用内存调整 `pageSize` 参数
3. **选择合适的格式**: JPEG格式比PNG占用更少内存
4. **关闭抗锯齿**: 对于大批量处理，可关闭抗锯齿以节省内存

### 性能优化
1. **降低DPI**: 对于预览用途，使用72-150 DPI
2. **批量处理**: 使用范围转换而不是逐页转换
3. **复用配置**: 避免重复创建配置对象

## 错误处理

工具类提供完善的错误处理机制：

```java
try {
    BufferedImage image = PdfToImageUtil.convertPdfPageToImage(pdfFile, 0);
} catch (PdfConvertException e) {
    log.error("PDF转换失败: {}", e.getMessage(), e);
}
```

常见异常：
- `PdfConvertException`: PDF转换相关异常
- `IllegalArgumentException`: 参数验证异常

## 依赖

- Apache PDFBox 2.0.29
- Hutool（Base64编码、文件操作）
- SLF4J（日志记录）
- Lombok（简化代码）

## 示例代码

详细的使用示例请参考 `PdfToImageExample.java` 文件，包含：
- 基本使用示例
- 高级配置示例
- 单页转换示例
- 分页处理示例
- 错误处理示例
- 性能测试示例

## 注意事项

1. **文件路径**: 确保PDF文件路径正确且文件存在
2. **权限**: 确保有读取PDF文件和写入输出目录的权限
3. **内存**: 处理大型PDF时建议使用分页方法
4. **格式**: 确保输出格式受支持
5. **页面索引**: 页面索引从0开始计算

## 更新日志

### v2.0.0 (2025-08-01)
- ✨ 新增获取PDF总页数功能
- ✨ 新增单页转换功能
- ✨ 新增页面范围转换功能
- ✨ 新增分页处理功能（内存友好）
- ✨ 新增Base64和数据URI转换功能
- 🚀 优化内存使用，支持大型PDF文件处理
- 📝 完善文档和示例代码

### v1.0.0
- 🎉 初始版本，支持基本的PDF转图片功能
