package com.cb.ai.data.analysis.voucher.utils.pdf;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * PDF转图片工具类
 * 提供将PDF文件转换为图片的各种方法
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2025-08-01
 */
@Slf4j
public class PdfToImageUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private PdfToImageUtil() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }

    /**
     * 获取PDF文件的总页数
     *
     * @param pdfFile PDF文件
     * @return 总页数
     * @throws PdfConvertException 转换异常
     */
    public static int getPdfPageCount(File pdfFile) {
        if (pdfFile == null || !pdfFile.exists()) {
            throw new PdfConvertException("PDF文件不存在: " + pdfFile);
        }
        if (!pdfFile.isFile()) {
            throw new PdfConvertException("指定路径不是文件: " + pdfFile);
        }

        try (FileInputStream fis = new FileInputStream(pdfFile)) {
            return getPdfPageCount(fis);
        } catch (IOException e) {
            throw new PdfConvertException("读取PDF文件失败: " + pdfFile.getAbsolutePath(), e);
        }
    }

    /**
     * 获取PDF输入流的总页数
     *
     * @param inputStream PDF输入流
     * @return 总页数
     * @throws PdfConvertException 转换异常
     */
    public static int getPdfPageCount(InputStream inputStream) {
        if (inputStream == null) {
            throw new PdfConvertException("PDF输入流不能为空");
        }

        try (PDDocument document = PDDocument.load(inputStream)) {
            return document.getNumberOfPages();
        } catch (IOException e) {
            throw new PdfConvertException("加载PDF文档失败", e);
        }
    }

    /**
     * 转换PDF的指定页面为BufferedImage（使用默认配置）
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @return BufferedImage
     * @throws PdfConvertException 转换异常
     */
    public static BufferedImage convertPdfPageToImage(File pdfFile, int pageIndex) {
        return convertPdfPageToImage(pdfFile, pageIndex, PdfToImageConfig.defaultConfig());
    }

    /**
     * 转换PDF的指定页面为BufferedImage
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @param config    转换配置
     * @return BufferedImage
     * @throws PdfConvertException 转换异常
     */
    public static BufferedImage convertPdfPageToImage(File pdfFile, int pageIndex, PdfToImageConfig config) {
        if (pdfFile == null || !pdfFile.exists()) {
            throw new PdfConvertException("PDF文件不存在: " + pdfFile);
        }
        if (!pdfFile.isFile()) {
            throw new PdfConvertException("指定路径不是文件: " + pdfFile);
        }

        try (FileInputStream fis = new FileInputStream(pdfFile)) {
            return convertPdfPageToImage(fis, pageIndex, config);
        } catch (IOException e) {
            throw new PdfConvertException("读取PDF文件失败: " + pdfFile.getAbsolutePath(), e);
        }
    }

    /**
     * 转换PDF输入流的指定页面为BufferedImage
     *
     * @param inputStream PDF输入流
     * @param pageIndex   页面索引（从0开始）
     * @param config      转换配置
     * @return BufferedImage
     * @throws PdfConvertException 转换异常
     */
    public static BufferedImage convertPdfPageToImage(InputStream inputStream, int pageIndex, PdfToImageConfig config) {
        if (inputStream == null) {
            throw new PdfConvertException("PDF输入流不能为空");
        }
        if (config == null) {
            config = PdfToImageConfig.defaultConfig();
        }

        config.validate();

        try (PDDocument document = PDDocument.load(inputStream)) {
            int pageCount = document.getNumberOfPages();

            if (pageIndex < 0 || pageIndex >= pageCount) {
                throw new PdfConvertException("页面索引超出范围: " + pageIndex + "，总页数: " + pageCount);
            }

            PDFRenderer renderer = new PDFRenderer(document);
            BufferedImage image = renderer.renderImageWithDPI(pageIndex, config.getDpi(), ImageType.RGB);

            // 如果启用抗锯齿，进行图像处理
            if (config.isAntiAliasing()) {
                image = applyAntiAliasing(image);
            }

            log.debug("成功转换第{}页", pageIndex + 1);
            return image;

        } catch (IOException e) {
            throw new PdfConvertException("转换第" + (pageIndex + 1) + "页失败", e);
        }
    }

    /**
     * 转换PDF的指定页面范围为BufferedImage列表（使用默认配置）
     *
     * @param pdfFile   PDF文件
     * @param startPage 起始页面索引（从0开始，包含）
     * @param endPage   结束页面索引（从0开始，包含）
     * @return BufferedImage列表
     * @throws PdfConvertException 转换异常
     */
    public static List<BufferedImage> convertPdfPagesToImages(File pdfFile, int startPage, int endPage) {
        return convertPdfPagesToImages(pdfFile, startPage, endPage, PdfToImageConfig.defaultConfig());
    }

    /**
     * 转换PDF的指定页面范围为BufferedImage列表
     *
     * @param pdfFile   PDF文件
     * @param startPage 起始页面索引（从0开始，包含）
     * @param endPage   结束页面索引（从0开始，包含）
     * @param config    转换配置
     * @return BufferedImage列表
     * @throws PdfConvertException 转换异常
     */
    public static List<BufferedImage> convertPdfPagesToImages(File pdfFile, int startPage, int endPage, PdfToImageConfig config) {
        if (pdfFile == null || !pdfFile.exists()) {
            throw new PdfConvertException("PDF文件不存在: " + pdfFile);
        }
        if (!pdfFile.isFile()) {
            throw new PdfConvertException("指定路径不是文件: " + pdfFile);
        }

        try (FileInputStream fis = new FileInputStream(pdfFile)) {
            return convertPdfPagesToImages(fis, startPage, endPage, config);
        } catch (IOException e) {
            throw new PdfConvertException("读取PDF文件失败: " + pdfFile.getAbsolutePath(), e);
        }
    }

    /**
     * 转换PDF输入流的指定页面范围为BufferedImage列表
     *
     * @param inputStream PDF输入流
     * @param startPage   起始页面索引（从0开始，包含）
     * @param endPage     结束页面索引（从0开始，包含）
     * @param config      转换配置
     * @return BufferedImage列表
     * @throws PdfConvertException 转换异常
     */
    public static List<BufferedImage> convertPdfPagesToImages(InputStream inputStream, int startPage, int endPage, PdfToImageConfig config) {
        if (inputStream == null) {
            throw new PdfConvertException("PDF输入流不能为空");
        }
        if (config == null) {
            config = PdfToImageConfig.defaultConfig();
        }

        config.validate();

        if (startPage < 0) {
            throw new PdfConvertException("起始页面索引不能小于0: " + startPage);
        }
        if (endPage < startPage) {
            throw new PdfConvertException("结束页面索引不能小于起始页面索引: " + endPage + " < " + startPage);
        }

        List<BufferedImage> images = new ArrayList<>();

        try (PDDocument document = PDDocument.load(inputStream)) {
            int pageCount = document.getNumberOfPages();

            if (startPage >= pageCount) {
                throw new PdfConvertException("起始页面索引超出范围: " + startPage + "，总页数: " + pageCount);
            }

            // 调整结束页面索引，不超过总页数
            int actualEndPage = Math.min(endPage, pageCount - 1);

            PDFRenderer renderer = new PDFRenderer(document);

            log.info("开始转换PDF页面范围 {}-{}，共{}页，DPI: {}",
                startPage + 1, actualEndPage + 1, actualEndPage - startPage + 1, config.getDpi());

            for (int pageIndex = startPage; pageIndex <= actualEndPage; pageIndex++) {
                try {
                    BufferedImage image = renderer.renderImageWithDPI(
                        pageIndex,
                        config.getDpi(),
                        ImageType.RGB
                    );

                    // 如果启用抗锯齿，进行图像处理
                    if (config.isAntiAliasing()) {
                        image = applyAntiAliasing(image);
                    }

                    images.add(image);
                    log.debug("成功转换第{}页", pageIndex + 1);
                } catch (IOException e) {
                    log.error("转换第{}页失败", pageIndex + 1, e);
                    throw new PdfConvertException("转换第" + (pageIndex + 1) + "页失败", e);
                }
            }

            log.info("PDF页面范围转换完成，共转换{}页", images.size());
            return images;

        } catch (IOException e) {
            throw new PdfConvertException("加载PDF文档失败", e);
        }
    }

    /**
     * 将PDF文件转换为BufferedImage列表（使用默认配置）
     * 
     * @param pdfFile PDF文件
     * @return BufferedImage列表，每个元素代表PDF的一页
     * @throws PdfConvertException 转换异常
     */
    public static List<BufferedImage> convertPdfToImages(File pdfFile) {
        return convertPdfToImages(pdfFile, PdfToImageConfig.defaultConfig());
    }

    /**
     * 将PDF文件转换为BufferedImage列表
     * 
     * @param pdfFile PDF文件
     * @param config  转换配置
     * @return BufferedImage列表，每个元素代表PDF的一页
     * @throws PdfConvertException 转换异常
     */
    public static List<BufferedImage> convertPdfToImages(File pdfFile, PdfToImageConfig config) {
        if (pdfFile == null || !pdfFile.exists()) {
            throw new PdfConvertException("PDF文件不存在: " + pdfFile);
        }
        if (!pdfFile.isFile()) {
            throw new PdfConvertException("指定路径不是文件: " + pdfFile);
        }

        try (FileInputStream fis = new FileInputStream(pdfFile)) {
            return convertPdfToImages(fis, config);
        } catch (IOException e) {
            throw new PdfConvertException("读取PDF文件失败: " + pdfFile.getAbsolutePath(), e);
        }
    }

    /**
     * 将PDF输入流转换为BufferedImage列表
     * 
     * @param inputStream PDF输入流
     * @param config      转换配置
     * @return BufferedImage列表，每个元素代表PDF的一页
     * @throws PdfConvertException 转换异常
     */
    public static List<BufferedImage> convertPdfToImages(InputStream inputStream, PdfToImageConfig config) {
        if (inputStream == null) {
            throw new PdfConvertException("PDF输入流不能为空");
        }
        if (config == null) {
            config = PdfToImageConfig.defaultConfig();
        }
        
        config.validate();
        
        List<BufferedImage> images = new ArrayList<>();
        
        try (PDDocument document = PDDocument.load(inputStream)) {
            PDFRenderer renderer = new PDFRenderer(document);
            int pageCount = document.getNumberOfPages();
            
            log.info("开始转换PDF，共{}页，DPI: {}", pageCount, config.getDpi());
            
            for (int pageIndex = 0; pageIndex < pageCount; pageIndex++) {
                try {
                    BufferedImage image = renderer.renderImageWithDPI(
                        pageIndex, 
                        config.getDpi(), 
                        ImageType.RGB
                    );
                    
                    // 如果启用抗锯齿，进行图像处理
                    if (config.isAntiAliasing()) {
                        image = applyAntiAliasing(image);
                    }
                    
                    images.add(image);
                    log.debug("成功转换第{}页", pageIndex + 1);
                } catch (IOException e) {
                    log.error("转换第{}页失败", pageIndex + 1, e);
                    throw new PdfConvertException("转换第" + (pageIndex + 1) + "页失败", e);
                }
            }
            
            log.info("PDF转换完成，共转换{}页", images.size());
            return images;
            
        } catch (IOException e) {
            throw new PdfConvertException("加载PDF文档失败", e);
        }
    }

    /**
     * 将PDF文件转换为图片文件并保存到指定目录
     * 
     * @param pdfFile   PDF文件
     * @param outputDir 输出目录
     * @return 生成的图片文件列表
     * @throws PdfConvertException 转换异常
     */
    public static List<File> convertPdfToImageFiles(File pdfFile, String outputDir) {
        return convertPdfToImageFiles(pdfFile, outputDir, PdfToImageConfig.defaultConfig());
    }

    /**
     * 将PDF文件转换为图片文件并保存到指定目录
     * 
     * @param pdfFile   PDF文件
     * @param outputDir 输出目录
     * @param config    转换配置
     * @return 生成的图片文件列表
     * @throws PdfConvertException 转换异常
     */
    public static List<File> convertPdfToImageFiles(File pdfFile, String outputDir, PdfToImageConfig config) {
        if (StrUtil.isBlank(outputDir)) {
            throw new PdfConvertException("输出目录不能为空");
        }
        
        // 确保输出目录存在
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            boolean created = outputDirFile.mkdirs();
            if (!created) {
                throw new PdfConvertException("创建输出目录失败: " + outputDir);
            }
        }
        
        List<BufferedImage> images = convertPdfToImages(pdfFile, config);
        List<File> imageFiles = new ArrayList<>();
        
        String baseName = FileUtil.getPrefix(pdfFile);
        String format = config.getFormat().toLowerCase();
        
        for (int i = 0; i < images.size(); i++) {
            String fileName = String.format("%s_page_%03d.%s", baseName, i + 1, format);
            File imageFile = new File(outputDirFile, fileName);
            
            try {
                saveImageToFile(images.get(i), imageFile, config);
                imageFiles.add(imageFile);
                log.debug("保存图片文件: {}", imageFile.getAbsolutePath());
            } catch (IOException e) {
                throw new PdfConvertException("保存图片文件失败: " + imageFile.getAbsolutePath(), e);
            }
        }
        
        log.info("PDF转换完成，生成{}个图片文件", imageFiles.size());
        return imageFiles;
    }

    /**
     * 将PDF文件转换为图片字节数组列表
     * 
     * @param pdfFile PDF文件
     * @return 图片字节数组列表
     * @throws PdfConvertException 转换异常
     */
    public static List<byte[]> convertPdfToImageBytes(File pdfFile) {
        return convertPdfToImageBytes(pdfFile, PdfToImageConfig.defaultConfig());
    }

    /**
     * 将PDF文件转换为图片字节数组列表
     *
     * @param pdfFile PDF文件
     * @param config  转换配置
     * @return 图片字节数组列表
     * @throws PdfConvertException 转换异常
     */
    public static List<byte[]> convertPdfToImageBytes(File pdfFile, PdfToImageConfig config) {
        List<BufferedImage> images = convertPdfToImages(pdfFile, config);
        List<byte[]> imageBytes = new ArrayList<>();

        for (BufferedImage image : images) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                saveImageToStream(image, baos, config);
                imageBytes.add(baos.toByteArray());
            } catch (IOException e) {
                throw new PdfConvertException("转换图片为字节数组失败", e);
            }
        }

        return imageBytes;
    }

    /**
     * 转换PDF的指定页面为图片文件
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @param outputFile 输出文件
     * @throws PdfConvertException 转换异常
     */
    public static void convertPdfPageToImageFile(File pdfFile, int pageIndex, File outputFile) {
        convertPdfPageToImageFile(pdfFile, pageIndex, outputFile, PdfToImageConfig.defaultConfig());
    }

    /**
     * 转换PDF的指定页面为图片文件
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @param outputFile 输出文件
     * @param config    转换配置
     * @throws PdfConvertException 转换异常
     */
    public static void convertPdfPageToImageFile(File pdfFile, int pageIndex, File outputFile, PdfToImageConfig config) {
        BufferedImage image = convertPdfPageToImage(pdfFile, pageIndex, config);

        try {
            saveImageToFile(image, outputFile, config);
            log.debug("保存图片文件: {}", outputFile.getAbsolutePath());
        } catch (IOException e) {
            throw new PdfConvertException("保存图片文件失败: " + outputFile.getAbsolutePath(), e);
        }
    }

    /**
     * 转换PDF的指定页面为字节数组
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @return 图片字节数组
     * @throws PdfConvertException 转换异常
     */
    public static byte[] convertPdfPageToImageBytes(File pdfFile, int pageIndex) {
        return convertPdfPageToImageBytes(pdfFile, pageIndex, PdfToImageConfig.defaultConfig());
    }

    /**
     * 转换PDF的指定页面为字节数组
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @param config    转换配置
     * @return 图片字节数组
     * @throws PdfConvertException 转换异常
     */
    public static byte[] convertPdfPageToImageBytes(File pdfFile, int pageIndex, PdfToImageConfig config) {
        BufferedImage image = convertPdfPageToImage(pdfFile, pageIndex, config);

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            saveImageToStream(image, baos, config);
            return baos.toByteArray();
        } catch (IOException e) {
            throw new PdfConvertException("转换图片为字节数组失败", e);
        }
    }

    /**
     * 转换PDF的指定页面为Base64字符串
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @return Base64字符串
     * @throws PdfConvertException 转换异常
     */
    public static String convertPdfPageToBase64(File pdfFile, int pageIndex) {
        return convertPdfPageToBase64(pdfFile, pageIndex, PdfToImageConfig.defaultConfig());
    }

    /**
     * 转换PDF的指定页面为Base64字符串
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @param config    转换配置
     * @return Base64字符串
     * @throws PdfConvertException 转换异常
     */
    public static String convertPdfPageToBase64(File pdfFile, int pageIndex, PdfToImageConfig config) {
        byte[] imageBytes = convertPdfPageToImageBytes(pdfFile, pageIndex, config);
        return Base64.encode(imageBytes);
    }

    /**
     * 转换PDF的指定页面为数据URI
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @return 数据URI字符串
     * @throws PdfConvertException 转换异常
     */
    public static String convertPdfPageToDataUri(File pdfFile, int pageIndex) {
        return convertPdfPageToDataUri(pdfFile, pageIndex, PdfToImageConfig.defaultConfig());
    }

    /**
     * 转换PDF的指定页面为数据URI
     *
     * @param pdfFile   PDF文件
     * @param pageIndex 页面索引（从0开始）
     * @param config    转换配置
     * @return 数据URI字符串
     * @throws PdfConvertException 转换异常
     */
    public static String convertPdfPageToDataUri(File pdfFile, int pageIndex, PdfToImageConfig config) {
        String base64 = convertPdfPageToBase64(pdfFile, pageIndex, config);
        String mimeType = getMimeType(config.getFormat());
        return String.format("data:%s;base64,%s", mimeType, base64);
    }

    /**
     * 分页转换PDF为图片文件（内存友好）
     *
     * @param pdfFile   PDF文件
     * @param outputDir 输出目录
     * @param pageSize  每批处理的页数
     * @return 生成的图片文件列表
     * @throws PdfConvertException 转换异常
     */
    public static List<File> convertPdfToImageFilesPaged(File pdfFile, String outputDir, int pageSize) {
        return convertPdfToImageFilesPaged(pdfFile, outputDir, pageSize, PdfToImageConfig.defaultConfig());
    }

    /**
     * 分页转换PDF为图片文件（内存友好）
     *
     * @param pdfFile   PDF文件
     * @param outputDir 输出目录
     * @param pageSize  每批处理的页数
     * @param config    转换配置
     * @return 生成的图片文件列表
     * @throws PdfConvertException 转换异常
     */
    public static List<File> convertPdfToImageFilesPaged(File pdfFile, String outputDir, int pageSize, PdfToImageConfig config) {
        if (StrUtil.isBlank(outputDir)) {
            throw new PdfConvertException("输出目录不能为空");
        }
        if (pageSize <= 0) {
            throw new PdfConvertException("每批处理页数必须大于0: " + pageSize);
        }

        // 确保输出目录存在
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            boolean created = outputDirFile.mkdirs();
            if (!created) {
                throw new PdfConvertException("创建输出目录失败: " + outputDir);
            }
        }

        int totalPages = getPdfPageCount(pdfFile);
        List<File> allImageFiles = new ArrayList<>();
        String baseName = FileUtil.getPrefix(pdfFile);
        String format = config.getFormat().toLowerCase();

        log.info("开始分页转换PDF，总页数: {}，每批处理: {}页", totalPages, pageSize);

        for (int startPage = 0; startPage < totalPages; startPage += pageSize) {
            int endPage = Math.min(startPage + pageSize - 1, totalPages - 1);

            log.info("处理页面范围: {} - {}", startPage + 1, endPage + 1);

            List<BufferedImage> batchImages = convertPdfPagesToImages(pdfFile, startPage, endPage, config);

            for (int i = 0; i < batchImages.size(); i++) {
                int actualPageIndex = startPage + i;
                String fileName = String.format("%s_page_%03d.%s", baseName, actualPageIndex + 1, format);
                File imageFile = new File(outputDirFile, fileName);

                try {
                    saveImageToFile(batchImages.get(i), imageFile, config);
                    allImageFiles.add(imageFile);
                    log.debug("保存图片文件: {}", imageFile.getAbsolutePath());
                } catch (IOException e) {
                    throw new PdfConvertException("保存图片文件失败: " + imageFile.getAbsolutePath(), e);
                }
            }

            // 清理当前批次的图片，释放内存
            batchImages.clear();

            // 建议垃圾回收
            if (startPage + pageSize < totalPages) {
                System.gc();
            }
        }

        log.info("分页转换完成，生成{}个图片文件", allImageFiles.size());
        return allImageFiles;
    }

    /**
     * 分页转换PDF为Base64字符串（内存友好）
     *
     * @param pdfFile  PDF文件
     * @param pageSize 每批处理的页数
     * @return Base64字符串列表
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToBase64Paged(File pdfFile, int pageSize) {
        return convertPdfToBase64Paged(pdfFile, pageSize, PdfToImageConfig.defaultConfig());
    }

    /**
     * 分页转换PDF为Base64字符串（内存友好）
     *
     * @param pdfFile  PDF文件
     * @param pageSize 每批处理的页数
     * @param config   转换配置
     * @return Base64字符串列表
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToBase64Paged(File pdfFile, int pageSize, PdfToImageConfig config) {
        if (pageSize <= 0) {
            throw new PdfConvertException("每批处理页数必须大于0: " + pageSize);
        }

        int totalPages = getPdfPageCount(pdfFile);
        List<String> allBase64Strings = new ArrayList<>();

        log.info("开始分页转换PDF为Base64，总页数: {}，每批处理: {}页", totalPages, pageSize);

        for (int startPage = 0; startPage < totalPages; startPage += pageSize) {
            int endPage = Math.min(startPage + pageSize - 1, totalPages - 1);

            log.debug("处理页面范围: {} - {}", startPage + 1, endPage + 1);

            List<BufferedImage> batchImages = convertPdfPagesToImages(pdfFile, startPage, endPage, config);

            for (BufferedImage image : batchImages) {
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                    saveImageToStream(image, baos, config);
                    byte[] bytes = baos.toByteArray();
                    String base64 = Base64.encode(bytes);
                    allBase64Strings.add(base64);
                } catch (IOException e) {
                    throw new PdfConvertException("转换图片为Base64失败", e);
                }
            }

            // 清理当前批次的图片，释放内存
            batchImages.clear();

            // 建议垃圾回收
            if (startPage + pageSize < totalPages) {
                System.gc();
            }
        }

        log.info("分页转换为Base64完成，共转换{}页", allBase64Strings.size());
        return allBase64Strings;
    }

    /**
     * 将PDF文件转换为Base64字符串列表（使用默认配置）
     *
     * @param pdfFile PDF文件
     * @return Base64字符串列表，每个元素代表PDF的一页图片的Base64编码
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToBase64(File pdfFile) {
        return convertPdfToBase64(pdfFile, PdfToImageConfig.defaultConfig());
    }

    /**
     * 将PDF文件转换为Base64字符串列表
     *
     * @param pdfFile PDF文件
     * @param config  转换配置
     * @return Base64字符串列表，每个元素代表PDF的一页图片的Base64编码
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToBase64(File pdfFile, PdfToImageConfig config) {
        List<byte[]> imageBytes = convertPdfToImageBytes(pdfFile, config);
        List<String> base64Strings = new ArrayList<>();

        for (byte[] bytes : imageBytes) {
            String base64 = Base64.encode(bytes);
            base64Strings.add(base64);
        }

        log.info("PDF转换为Base64完成，共转换{}页", base64Strings.size());
        return base64Strings;
    }

    /**
     * 将PDF输入流转换为Base64字符串列表
     *
     * @param inputStream PDF输入流
     * @param config      转换配置
     * @return Base64字符串列表，每个元素代表PDF的一页图片的Base64编码
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToBase64(InputStream inputStream, PdfToImageConfig config) {
        List<BufferedImage> images = convertPdfToImages(inputStream, config);
        List<String> base64Strings = new ArrayList<>();

        for (BufferedImage image : images) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                saveImageToStream(image, baos, config);
                byte[] bytes = baos.toByteArray();
                String base64 = Base64.encode(bytes);
                base64Strings.add(base64);
            } catch (IOException e) {
                throw new PdfConvertException("转换图片为Base64失败", e);
            }
        }

        log.info("PDF转换为Base64完成，共转换{}页", base64Strings.size());
        return base64Strings;
    }

    /**
     * 将PDF文件转换为带数据URI前缀的Base64字符串列表（使用默认配置）
     * 数据URI格式：data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
     *
     * @param pdfFile PDF文件
     * @return 带数据URI前缀的Base64字符串列表
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToDataUri(File pdfFile) {
        return convertPdfToDataUri(pdfFile, PdfToImageConfig.defaultConfig());
    }

    /**
     * 将PDF文件转换为带数据URI前缀的Base64字符串列表
     * 数据URI格式：data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
     *
     * @param pdfFile PDF文件
     * @param config  转换配置
     * @return 带数据URI前缀的Base64字符串列表
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToDataUri(File pdfFile, PdfToImageConfig config) {
        List<String> base64Strings = convertPdfToBase64(pdfFile, config);
        List<String> dataUris = new ArrayList<>();

        String mimeType = getMimeType(config.getFormat());

        for (String base64 : base64Strings) {
            String dataUri = String.format("data:%s;base64,%s", mimeType, base64);
            dataUris.add(dataUri);
        }

        log.info("PDF转换为数据URI完成，共转换{}页", dataUris.size());
        return dataUris;
    }

    /**
     * 将PDF输入流转换为带数据URI前缀的Base64字符串列表
     * 数据URI格式：data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
     *
     * @param inputStream PDF输入流
     * @param config      转换配置
     * @return 带数据URI前缀的Base64字符串列表
     * @throws PdfConvertException 转换异常
     */
    public static List<String> convertPdfToDataUri(InputStream inputStream, PdfToImageConfig config) {
        List<String> base64Strings = convertPdfToBase64(inputStream, config);
        List<String> dataUris = new ArrayList<>();

        String mimeType = getMimeType(config.getFormat());

        for (String base64 : base64Strings) {
            String dataUri = String.format("data:%s;base64,%s", mimeType, base64);
            dataUris.add(dataUri);
        }

        log.info("PDF转换为数据URI完成，共转换{}页", dataUris.size());
        return dataUris;
    }

    /**
     * 获取图片格式对应的MIME类型
     *
     * @param format 图片格式
     * @return MIME类型
     */
    private static String getMimeType(String format) {
        String upperFormat = format.toUpperCase();
        switch (upperFormat) {
            case "PNG":
                return "image/png";
            case "JPG":
            case "JPEG":
                return "image/jpeg";
            case "BMP":
                return "image/bmp";
            case "GIF":
                return "image/gif";
            default:
                return "image/png"; // 默认返回PNG
        }
    }

    /**
     * 应用抗锯齿处理
     *
     * @param originalImage 原始图像
     * @return 处理后的图像
     */
    private static BufferedImage applyAntiAliasing(BufferedImage originalImage) {
        BufferedImage antiAliasedImage = new BufferedImage(
            originalImage.getWidth(),
            originalImage.getHeight(),
            BufferedImage.TYPE_INT_RGB
        );

        Graphics2D g2d = antiAliasedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();

        return antiAliasedImage;
    }

    /**
     * 保存图片到文件
     * 
     * @param image  图片
     * @param file   文件
     * @param config 配置
     * @throws IOException IO异常
     */
    private static void saveImageToFile(BufferedImage image, File file, PdfToImageConfig config) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            saveImageToStream(image, fos, config);
        }
    }

    /**
     * 保存图片到输出流
     * 
     * @param image  图片
     * @param output 输出流
     * @param config 配置
     * @throws IOException IO异常
     */
    private static void saveImageToStream(BufferedImage image, OutputStream output, PdfToImageConfig config) throws IOException {
        String format = config.getFormat().toUpperCase();
        
        if ("JPEG".equals(format) || "JPG".equals(format)) {
            // JPEG格式需要特殊处理质量参数
            saveJpegWithQuality(image, output, config.getQuality());
        } else {
            // 其他格式直接保存
            ImageIO.write(image, format, output);
        }
    }

    /**
     * 保存JPEG格式图片并设置质量
     * 
     * @param image   图片
     * @param output  输出流
     * @param quality 质量（0.0-1.0）
     * @throws IOException IO异常
     */
    private static void saveJpegWithQuality(BufferedImage image, OutputStream output, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("JPEG");
        if (!writers.hasNext()) {
            throw new IOException("没有找到JPEG图片写入器");
        }
        
        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(quality);
        
        try (ImageOutputStream ios = ImageIO.createImageOutputStream(output)) {
            writer.setOutput(ios);
            writer.write(null, new javax.imageio.IIOImage(image, null, null), param);
        } finally {
            writer.dispose();
        }
    }
}
